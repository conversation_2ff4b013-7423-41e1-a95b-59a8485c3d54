{% extends "student/base.html" %}

{% block title %}{{ translations.payment }}{% endblock %}

{% block content %}
<div class="dashboard-header">
    <h1>{{ translations.payment }}</h1>
    <p>{{ translations.choose_payment_method }}</p>
</div>

<div class="card">
    <div class="card-header">
        <h2>{{ translations.payment_details }}</h2>
    </div>
    <div class="card-body">
        <div class="summary-item">
            <div class="summary-label">{{ translations.academic_years }}:</div>
            <div class="summary-value">{{ request.academic_years|join(', ') }}</div>
        </div>
        <div class="summary-item">
            <div class="summary-label">{{ translations.number_of_transcripts }}:</div>
            <div class="summary-value">{{ request.count }}</div>
        </div>
        <div class="summary-item total-row">
            <div class="summary-label">{{ translations.total_amount }}:</div>
            <div class="summary-value">{{ request.total_price }} RWF</div>
        </div>

        <form action="{{ url_for('payment') }}" method="POST">
            <div class="form-group" style="margin-top: 30px;">
                <label>{{ translations.select_payment_method }}</label>
                
                <div class="payment-methods">
                    <div class="payment-method selected" onclick="selectPaymentMethod('mobile_money')">
                        <img src="{{ url_for('static', filename='images/momo.png') }}" alt="Mobile Money">
                        <h3>Mobile Money</h3>
                        <p>Pay securely using Mobile Money (MTN, Airtel, etc.)</p>
                        <input type="radio" name="payment_method" value="mobile_money" id="mobile_money" checked style="display: none;">
                    </div>
                </div>
            </div>
            
            <div id="mobile_money-details" class="payment-details" style="display: block;">
                <div class="form-group">
                    <label for="phone">{{ translations.mobile_money_phone }}</label>
                    <div class="input-group">
                        <span class="input-icon"><i class="fas fa-phone"></i></span>
                        <input type="tel" id="phone" name="phone" placeholder="{{ translations.phone_placeholder }}" required>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <button type="submit" class="btn btn-primary btn-block" id="pay-button">
                    <i class="fas fa-lock"></i> {{ translations.pay_now }}
                </button>
            </div>

            <div class="form-group">
                <a href="{{ url_for('request_summary') }}" class="btn btn-secondary btn-block">
                    <i class="fas fa-arrow-left"></i> {{ translations.back_to_summary }}
                </a>
            </div>
        </form>
    </div>
    <div class="card-footer">
        <p><i class="fas fa-shield-alt"></i> {{ translations.secure_payment_note }}</p>
    </div>
</div>

<script>
    // Payment method selection
    function selectPaymentMethod(method) {
        // Reset all payment methods
        document.querySelectorAll('.payment-method').forEach(el => {
            el.classList.remove('selected');
        });
        
        document.querySelectorAll('.payment-details').forEach(el => {
            el.style.display = 'none';
        });
        
        // Select the chosen method
        document.getElementById(method).checked = true;
        document.querySelector(`.payment-method:has(#${method})`).classList.add('selected');
        
        // Show the relevant details
        document.getElementById(`${method}-details`).style.display = 'block';
        
        // Enable the pay button
        document.getElementById('pay-button').disabled = false;
    }
    
    // Form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(event) {
        const phoneInput = document.getElementById('phone');
        if (!phoneInput.value) {
            event.preventDefault();
            alert('{{ translations.enter_phone_number }}');
        }
    });

    // Initialize - mobile money is pre-selected
    document.addEventListener('DOMContentLoaded', function() {
        selectPaymentMethod('mobile_money');
    });
</script>
{% endblock %}

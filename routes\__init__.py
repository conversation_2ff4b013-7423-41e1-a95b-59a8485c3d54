"""
Route registration for INES Transcript System
Modular blueprint organization
"""
from .auth import auth_bp
from .student import student_bp
from .finance import finance_bp
from .faculty import faculty_bp
from .api import api_bp
from .main import main_bp

def register_blueprints(app):
    """Register all application blueprints"""
    app.register_blueprint(main_bp)
    app.register_blueprint(auth_bp, url_prefix='/auth')
    app.register_blueprint(student_bp, url_prefix='/student')
    app.register_blueprint(finance_bp, url_prefix='/finance')
    app.register_blueprint(faculty_bp, url_prefix='/faculty')
    app.register_blueprint(api_bp, url_prefix='/api')
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Faculty Dashboard | INES-Ruhengeri Transcript System</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .sidebar-header {
            text-align: center;
            padding: 20px;
            border-bottom: 1px solid #e0e0e0;
        }
        .profile-image {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #1976D2;
            margin-bottom: 15px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .sidebar-header h2 {
            color: #fff;
            font-size: 1.2rem;
            margin: 10px 0 5px;
            font-weight: 600;
        }
        .sidebar-header p {
            color: #fff;
            font-size: 0.9rem;
            margin: 0;
        }
        .dashboard-header h1 {
            color: #333;
            font-size: 1.8rem;
            margin-bottom: 10px;
        }
        .dashboard-header p {
            color: #666;
            font-size: 1rem;
        }

        /* Enhanced Faculty Dashboard Styles */
        .action-buttons {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .action-buttons .btn {
            transition: all 0.3s ease;
            font-weight: 600;
            border-radius: 20px;
            padding: 8px 16px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .action-buttons .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 0.85rem;
            font-weight: 600;
        }

        .status-badge img {
            width: 18px !important;
            height: 18px !important;
        }

        /* Status-specific styling */
        .status-badge.status-pending {
            background: rgba(255, 193, 7, 0.1);
            color: #856404;
            border: 1px solid rgba(255, 193, 7, 0.3);
        }

        .status-badge.status-completed {
            background: rgba(40, 167, 69, 0.1);
            color: #155724;
            border: 1px solid rgba(40, 167, 69, 0.3);
        }

        .status-badge.status-done {
            background: rgba(23, 162, 184, 0.1);
            color: #0c5460;
            border: 1px solid rgba(23, 162, 184, 0.3);
        }

        /* Table improvements */
        .table th, table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
            border-bottom: 2px solid #dee2e6;
            padding: 12px;
        }

        .table td, table td {
            padding: 12px;
            vertical-align: middle;
        }

        .table tbody tr:hover, table tbody tr:hover {
            background: #f8f9fa;
        }

        /* Badge styling for sidebar */
        .badge {
            background: #dc3545;
            color: white;
            border-radius: 50%;
            padding: 2px 6px;
            font-size: 0.7rem;
            margin-left: 5px;
        }

        /* Modern hover effects */
        .stat-card:hover {
            transform: translateY(-5px);
        }

        .requests-table tr:hover {
            background: #f8f9fa !important;
        }

        .action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(25, 118, 210, 0.3);
            color: white !important;
            text-decoration: none !important;
        }

        .action-btn.secondary:hover {
            background: linear-gradient(135deg, #4DD0E1, #00BCD4) !important;
            box-shadow: 0 8px 25px rgba(0, 188, 212, 0.3);
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="{{ url_for('static', filename='images/user.jpeg') }}" alt="Faculty Profile" class="profile-image">
                <h2>{{ session.name }}</h2>
                <p>Faculty ID: {{ session.user_id }}</p>
            </div>
            
            <nav class="sidebar-menu">
                <ul>
                    <li>
                        <a href="{{ url_for('faculty_dashboard') }}" class="active">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                            {% if pending_count > 0 %}
                                <span class="badge">{{ pending_count }}</span>
                            {% endif %}
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('upload_transcript') }}">
                            <i class="fas fa-upload"></i> Upload Transcript
                            {% if pending_count > 0 %}
                                <span class="badge">{{ pending_count }}</span>
                            {% endif %}
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('request_history') }}">
                            <i class="fas fa-history"></i> Request History
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('logout') }}">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>
        
        <!-- Main Content -->
        <main class="main-content">
            <div class="top-bar">
                <button class="menu-toggle" id="menu-toggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }}">
                            <i class="fas fa-{% if category == 'success' %}check-circle{% elif category == 'error' %}exclamation-circle{% elif category == 'warning' %}exclamation-triangle{% else %}info-circle{% endif %}"></i>
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
            
            <!-- Dashboard Header -->
            <div class="dashboard-header" style="background: white; padding: 30px; border-radius: 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); margin-bottom: 30px; text-align: center;">
                <h1 style="color: #333; font-size: 2rem; margin-bottom: 10px; font-weight: 700;">Faculty Dashboard</h1>
                <p style="color: #666; font-size: 1.1rem; margin: 0;">Manage academic transcript uploads and approvals</p>
            </div>

            <!-- Welcome Section -->
            <div class="welcome-card" style="background: linear-gradient(135deg, #1976D2, #00BCD4); color: white; border-radius: 15px; padding: 25px; margin-bottom: 30px; box-shadow: 0 4px 15px rgba(25, 118, 210, 0.3);">
                <h2 style="font-size: 1.5rem; margin-bottom: 10px; font-weight: 600;">Welcome, {{ session.name }}!</h2>
                <p style="font-size: 1.1rem; margin: 0; opacity: 0.9;">Faculty ID: {{ session.user_id }}</p>
            </div>

            <!-- Stats Grid -->
            <div class="stats-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 40px;">
                <div class="stat-card" style="background: white; border-radius: 15px; padding: 25px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); display: flex; align-items: center; transition: transform 0.3s ease;">
                    <div class="stat-icon pending-icon" style="width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 20px; font-size: 1.5rem; background: #fff3cd; color: #856404;">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-content">
                        <h3 style="font-size: 2rem; margin: 0; font-weight: 700; color: #856404;">{{ pending_count }}</h3>
                        <p style="margin: 0; font-size: 1rem; font-weight: 600; color: #495057;">Pending Uploads</p>
                    </div>
                </div>

                <div class="stat-card" style="background: white; border-radius: 15px; padding: 25px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); display: flex; align-items: center; transition: transform 0.3s ease;">
                    <div class="stat-icon completed-icon" style="width: 60px; height: 60px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 20px; font-size: 1.5rem; background: #d4edda; color: #155724;">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-content">
                        <h3 style="font-size: 2rem; margin: 0; font-weight: 700; color: #155724;">{{ completed_count }}</h3>
                        <p style="margin: 0; font-size: 1rem; font-weight: 600; color: #495057;">Total Uploaded</p>
                    </div>
                </div>
            </div>
            
            <!-- Pending Transcript Uploads -->
            <div class="recent-requests" style="background: white; border-radius: 15px; padding: 30px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); margin-bottom: 30px;">
                <div class="recent-requests-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 25px;">
                    <h3 style="color: #333; font-size: 1.4rem; margin: 0; font-weight: 600;">Pending Transcript Uploads</h3>
                </div>
                <div class="table-responsive">
                    <table class="requests-table" style="width: 100%; border-collapse: collapse; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                            <thead>
                                <tr>
                                    <th style="background: #f8f9fa; color: #333; padding: 15px 12px; text-align: left; font-weight: 600; border-bottom: 2px solid #e9ecef; font-size: 0.9rem;">Request ID</th>
                                    <th style="background: #f8f9fa; color: #333; padding: 15px 12px; text-align: left; font-weight: 600; border-bottom: 2px solid #e9ecef; font-size: 0.9rem;">Student Name</th>
                                    <th style="background: #f8f9fa; color: #333; padding: 15px 12px; text-align: left; font-weight: 600; border-bottom: 2px solid #e9ecef; font-size: 0.9rem;">Date</th>
                                    <th style="background: #f8f9fa; color: #333; padding: 15px 12px; text-align: left; font-weight: 600; border-bottom: 2px solid #e9ecef; font-size: 0.9rem;">Academic Years</th>
                                    <th style="background: #f8f9fa; color: #333; padding: 15px 12px; text-align: left; font-weight: 600; border-bottom: 2px solid #e9ecef; font-size: 0.9rem;">Status</th>
                                    <th style="background: #f8f9fa; color: #333; padding: 15px 12px; text-align: left; font-weight: 600; border-bottom: 2px solid #e9ecef; font-size: 0.9rem;">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if requests %}
                                    {% for request in requests %}
                                        <tr style="transition: background-color 0.3s ease;">
                                            <td style="padding: 15px 12px; border-bottom: 1px solid #e9ecef; color: #495057; vertical-align: middle;"><strong>{{ loop.index }}</strong></td>
                                            <td style="padding: 15px 12px; border-bottom: 1px solid #e9ecef; color: #495057; vertical-align: middle;">{{ request.student_name }}</td>
                                            <td style="padding: 15px 12px; border-bottom: 1px solid #e9ecef; color: #495057; vertical-align: middle;">{{ request.date }}</td>
                                            <td style="padding: 15px 12px; border-bottom: 1px solid #e9ecef; color: #495057; vertical-align: middle;">{{ request.academic_years|join(', ') }}</td>
                                            <td style="padding: 15px 12px; border-bottom: 1px solid #e9ecef; color: #495057; vertical-align: middle; text-align: center;">
                                                <img src="{{ url_for('static', filename='images/13.png') }}" alt="Pending Upload" style="width: 32px; height: 32px; vertical-align: middle;" title="Pending Upload">
                                            </td>
                                            <td style="padding: 15px 12px; border-bottom: 1px solid #e9ecef; color: #495057; vertical-align: middle;">
                                                <a href="{{ url_for('upload_transcript') }}" class="action-btn" style="background: linear-gradient(135deg, #1976D2, #1565C0); color: white; border: none; border-radius: 12px; padding: 10px 20px; text-decoration: none; display: inline-flex; align-items: center; justify-content: center; font-size: 0.9rem; font-weight: 600; transition: all 0.3s ease; min-height: 40px;">
                                                    <i class="fas fa-upload" style="margin-right: 8px;"></i>Upload Transcript
                                                </a>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="6" style="padding: 40px; text-align: center; color: #666; border-bottom: 1px solid #e9ecef;">
                                            <i class="fas fa-inbox" style="font-size: 3rem; color: #ccc; margin-bottom: 15px; display: block;"></i>
                                            No pending transcript uploads
                                        </td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="quick-actions" style="background: white; border-radius: 15px; padding: 30px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); margin-bottom: 30px;">
                <h3 style="color: #333; font-size: 1.4rem; margin-bottom: 25px; font-weight: 600; text-align: center;">Quick Actions</h3>
                <div class="actions-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                    <a href="{{ url_for('upload_transcript') }}" class="action-btn" style="background: linear-gradient(135deg, #1976D2, #1565C0); color: white; border: none; border-radius: 12px; padding: 20px; text-decoration: none; display: flex; align-items: center; justify-content: center; font-size: 1rem; font-weight: 600; transition: all 0.3s ease; min-height: 80px;">
                        <i class="fas fa-upload" style="margin-right: 10px; font-size: 1.2rem;"></i>Upload Transcripts
                    </a>
                    <a href="{{ url_for('request_history') }}" class="action-btn secondary" style="background: linear-gradient(135deg, #00BCD4, #0097A7); color: white; border: none; border-radius: 12px; padding: 20px; text-decoration: none; display: flex; align-items: center; justify-content: center; font-size: 1rem; font-weight: 600; transition: all 0.3s ease; min-height: 80px;">
                        <i class="fas fa-history" style="margin-right: 10px; font-size: 1.2rem;"></i>View Request History
                    </a>
                </div>
            </div>
        </main>
    </div>



    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Toggle sidebar on mobile
        const menuToggle = document.getElementById('menu-toggle');
        const sidebar = document.querySelector('.sidebar');
        
        menuToggle.addEventListener('click', function() {
            sidebar.classList.toggle('active');
        });
        
        // Close alerts after 5 seconds
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(alert => {
            setTimeout(() => {
                alert.style.display = 'none';
            }, 5000);
        });


    </script>
</body>
</html>

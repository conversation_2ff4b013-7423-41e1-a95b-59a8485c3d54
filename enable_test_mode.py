#!/usr/bin/env python3
"""
Enable test mode for MoMo API when the service is down
"""

import os

# Set environment variable for test mode
os.environ['MOMO_TEST_MODE'] = 'true'

print("✅ MoMo API Test Mode Enabled")
print("This will simulate successful payments when the real API is down.")
print("To disable test mode, restart the server without this script.")

# Test the API
try:
    from services.momo_api import IntouchMoMoAPI
    
    api = IntouchMoMoAPI()
    print(f"Test mode status: {api.test_mode}")
    
    if api.test_mode:
        print("🧪 Testing payment request...")
        result = api.request_to_pay("250788123456", 100)
        print(f"Payment result: {result}")
        
        if result['success']:
            print("🧪 Testing payment status...")
            status = api.check_payment_status(result['transaction_id'])
            print(f"Status result: {status}")
            
        print("\n🎉 Test mode is working correctly!")
    else:
        print("❌ Test mode not enabled properly")
        
except Exception as e:
    print(f"❌ Error testing API: {e}")

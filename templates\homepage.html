<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>INES Ruhengeri - Digital Transcript Hub</title>
    <style>
        /* Modern Reset */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
            overflow-x: hidden;
        }

        /* Floating Navigation */
        .floating-nav {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(135deg, #1976D2, #1565C0);
            border-radius: 50px;
            padding: 12px 30px;
            z-index: 1000;
            transition: all 0.3s ease;
            box-shadow: 0 4px 20px rgba(25, 118, 210, 0.3);
        }

        .floating-nav:hover {
            transform: translateX(-50%) translateY(-2px);
            box-shadow: 0 8px 30px rgba(25, 118, 210, 0.4);
        }

        .nav-content {
            display: flex;
            align-items: center;
            gap: 30px;
        }

        .nav-logo {
            display: flex;
            align-items: center;
            gap: 10px;
            color: white;
            text-decoration: none;
            font-weight: 700;
            font-size: 18px;
        }

        .nav-logo::before {
            content: "🎓";
            font-size: 24px;
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-5px); }
        }

        .nav-links {
            display: flex;
            gap: 25px;
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.9);
            text-decoration: none;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: 25px;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-link:hover {
            color: white;
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .nav-link.active {
            color: white;
            background: rgba(255, 255, 255, 0.2);
        }

        /* Hero Section */
        .hero {
            height: 100vh;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4)), url('{{ url_for("static", filename="images/home_image.jpg") }}');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(25, 118, 210, 0.1), rgba(21, 101, 192, 0.1));
            z-index: 1;
        }

        .hero-content {
            text-align: center;
            z-index: 2;
            max-width: 800px;
            padding: 0 20px;
        }

        .hero-badge {
            display: inline-block;
            background: rgba(25, 118, 210, 0.2);
            border: 1px solid rgba(25, 118, 210, 0.5);
            color: #1976D2;
            padding: 8px 20px;
            border-radius: 50px;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 30px;
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.05); opacity: 0.8; }
        }

        .hero-title {
            font-size: clamp(3rem, 8vw, 6rem);
            font-weight: 900;
            color: white;
            margin-bottom: 20px;
            line-height: 1.1;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }

        @keyframes titleGlow {
            0% { filter: brightness(1); }
            100% { filter: brightness(1.2); }
        }

        .hero-subtitle {
            font-size: clamp(1.2rem, 3vw, 1.8rem);
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 40px;
            font-weight: 400;
        }

        .hero-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 16px 32px;
            border: none;
            border-radius: 50px;
            font-weight: 600;
            font-size: 16px;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, #1976D2, #1565C0);
            color: white;
            box-shadow: 0 10px 30px rgba(25, 118, 210, 0.3);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #42A5F5, #1976D2);
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(25, 118, 210, 0.4);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-3px);
        }

        /* Floating Elements */
        .floating-shapes {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .shape {
            position: absolute;
            border-radius: 50%;
            background: linear-gradient(135deg, rgba(0, 255, 136, 0.1), rgba(0, 102, 255, 0.1));
            animation: float-shapes 20s linear infinite;
        }

        .shape:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .shape:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 60%;
            right: 10%;
            animation-delay: -5s;
        }

        .shape:nth-child(3) {
            width: 60px;
            height: 60px;
            top: 80%;
            left: 20%;
            animation-delay: -10s;
        }

        @keyframes float-shapes {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            25% { transform: translateY(-20px) rotate(90deg); }
            50% { transform: translateY(-40px) rotate(180deg); }
            75% { transform: translateY(-20px) rotate(270deg); }
        }

        /* Services Section */
        .services {
            padding: 120px 0;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            position: relative;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .section-header {
            text-align: center;
            margin-bottom: 80px;
        }

        .section-title {
            font-size: clamp(2.5rem, 5vw, 4rem);
            font-weight: 800;
            color: #333;
            margin-bottom: 20px;
        }

        .section-subtitle {
            font-size: 1.2rem;
            color: #666;
            max-width: 600px;
            margin: 0 auto;
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 40px;
        }

        .service-card {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 20px;
            padding: 40px 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .service-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #1976D2, #00BCD4);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .service-card:hover::before {
            transform: scaleX(1);
        }

        .service-card:hover {
            transform: translateY(-10px);
            border-color: rgba(25, 118, 210, 0.3);
            box-shadow: 0 20px 40px rgba(25, 118, 210, 0.15);
        }

        .service-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 25px;
            background: linear-gradient(135deg, #1976D2, #00BCD4);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            transition: all 0.3s ease;
        }

        .service-card:hover .service-icon {
            transform: scale(1.1) rotate(5deg);
            box-shadow: 0 10px 30px rgba(25, 118, 210, 0.3);
        }

        .service-card h3 {
            color: #333;
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 15px;
        }

        .service-card p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 25px;
        }

        .service-btn {
            background: rgba(25, 118, 210, 0.1);
            border: 1px solid rgba(25, 118, 210, 0.3);
            color: #1976D2;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .service-btn:hover {
            background: linear-gradient(135deg, #1976D2, #00BCD4);
            color: white;
            border-color: transparent;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(25, 118, 210, 0.3);
        }

        /* Footer */
        .footer {
            background: linear-gradient(rgba(0,0,0,0.95), rgba(0,0,0,0.95)), url('{{ url_for("static", filename="images/footer.png") }}');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            padding: 80px 0 30px;
            position: relative;
            color: white;
            overflow: hidden;
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.9);
            z-index: 0;
        }

        .footer-content {
            position: relative;
            z-index: 2;
        }

        .footer-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 50px;
            margin-bottom: 50px;
            background: rgba(0, 0, 0, 0.3);
            padding: 40px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }

        .footer-column h4 {
            color: #42A5F5;
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 25px;
            position: relative;
            text-shadow: 0 2px 4px rgba(0,0,0,0.5);
        }

        .footer-column h4::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 0;
            width: 40px;
            height: 2px;
            background: linear-gradient(90deg, #1976D2, #00BCD4);
        }

        .footer-column p,
        .footer-column li {
            color: rgba(255, 255, 255, 0.95);
            margin-bottom: 12px;
            line-height: 1.6;
            text-shadow: 0 1px 2px rgba(0,0,0,0.5);
            font-weight: 400;
        }

        .footer-column ul {
            list-style: none;
        }

        .footer-column a {
            color: rgba(255, 255, 255, 0.9);
            text-decoration: none;
            transition: all 0.3s ease;
            position: relative;
            text-shadow: 0 1px 2px rgba(0,0,0,0.5);
        }

        .footer-column a:hover {
            color: #42A5F5;
            transform: translateX(5px);
            text-shadow: 0 2px 4px rgba(66, 165, 245, 0.3);
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 15px;
            color: rgba(255, 255, 255, 0.95);
            text-shadow: 0 1px 2px rgba(0,0,0,0.5);
        }

        .contact-icon {
            width: 20px;
            height: 20px;
            background: linear-gradient(135deg, #1976D2, #00BCD4);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }

        .footer-bottom {
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding: 30px 40px 20px;
            text-align: center;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            backdrop-filter: blur(10px);
            margin-top: 20px;
        }

        .footer-bottom p {
            color: rgba(255, 255, 255, 0.95);
            font-size: 14px;
            font-weight: 500;
            text-shadow: 0 1px 2px rgba(0,0,0,0.5);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .floating-nav {
                top: 10px;
                left: 10px;
                right: 10px;
                transform: none;
                border-radius: 20px;
                padding: 15px 20px;
            }

            .nav-content {
                flex-direction: column;
                gap: 20px;
            }

            .nav-links {
                gap: 15px;
            }

            .hero-title {
                font-size: 3rem;
            }

            .hero-subtitle {
                font-size: 1.2rem;
            }

            .hero-buttons {
                flex-direction: column;
                align-items: center;
            }

            .services-grid {
                grid-template-columns: 1fr;
                gap: 30px;
            }

            .footer-grid {
                grid-template-columns: 1fr;
                gap: 40px;
            }
        }

        @media (max-width: 480px) {
            .hero {
                padding: 0 10px;
            }

            .hero-title {
                font-size: 2.5rem;
            }

            .section-title {
                font-size: 2.5rem;
            }

            .service-card {
                padding: 30px 20px;
            }

            .btn {
                padding: 14px 28px;
                font-size: 14px;
            }
        }

        /* Smooth scrolling */
        html {
            scroll-behavior: smooth;
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #1976D2, #00BCD4);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #42A5F5, #1976D2);
        }
    </style>
</head>
<body>
    <!-- Floating Navigation -->
    <nav class="floating-nav">
        <div class="nav-content">
            <a href="{{ url_for('homepage') }}" class="nav-logo">
                INES Ruhengeri
            </a>
            <div class="nav-links">
                <a href="{{ url_for('homepage') }}" class="nav-link active">Home</a>
                <a href="#services" class="nav-link">Services</a>
                <a href="#contact" class="nav-link">Contact</a>
                <a href="{{ url_for('login_page') }}" class="nav-link">Login</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
        <div class="floating-shapes">
            <div class="shape"></div>
            <div class="shape"></div>
            <div class="shape"></div>
        </div>

        <div class="hero-content">
            <div class="hero-badge">✨ Digital Transcript Hub</div>
            <h1 class="hero-title">INES Ruhengeri</h1>
            <p class="hero-subtitle">
                Next-Generation Academic Document Management<br>
                Secure • Fast • Reliable
            </p>
            <div class="hero-buttons">
                <a href="{{ url_for('login_page') }}" class="btn btn-primary">
                    🚀 Access Portal
                </a>
                <a href="#services" class="btn btn-secondary">
                    📋 Explore Services
                </a>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section class="services" id="services">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Digital Services</h2>
                <p class="section-subtitle">
                    Advanced academic document management powered by cutting-edge technology
                </p>
            </div>

            <div class="services-grid">
                <div class="service-card">
                    <div class="service-icon">🎓</div>
                    <h3>Student Portal</h3>
                    <p>Seamlessly access your academic records, request official transcripts, and track your educational journey through our intuitive student dashboard.</p>
                    <button class="service-btn">Access Portal</button>
                </div>

                <div class="service-card">
                    <div class="service-icon">📄</div>
                    <h3>Transcript Management</h3>
                    <p>State-of-the-art digital transcript processing with blockchain verification, ensuring authenticity and instant global recognition.</p>
                    <button class="service-btn">Learn More</button>
                </div>

                <div class="service-card">
                    <div class="service-icon">👨‍🏫</div>
                    <h3>Faculty Hub</h3>
                    <p>Comprehensive faculty tools for managing student records, uploading verified transcripts, and monitoring academic progress in real-time.</p>
                    <button class="service-btn">Faculty Access</button>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer" id="contact">
        <div class="footer-content">
            <div class="container">
                <div class="footer-grid">
                    <div class="footer-column">
                        <h4>INES Ruhengeri</h4>
                        <p>Institut d'Enseignement Supérieur de Ruhengeri</p>
                        <p>Pioneering the future of digital education with innovative transcript management solutions and academic excellence.</p>
                    </div>

                    <div class="footer-column">
                        <h4>Quick Access</h4>
                        <ul>
                            <li><a href="{{ url_for('homepage') }}">🏠 Home</a></li>
                            <li><a href="{{ url_for('login_page') }}">🎓 Student Portal</a></li>
                            <li><a href="{{ url_for('login_page') }}">👨‍🏫 Faculty Portal</a></li>
                            <li><a href="{{ url_for('login_page') }}">💰 Finance Portal</a></li>
                            <li><a href="#services">📋 Services</a></li>
                        </ul>
                    </div>

                    <div class="footer-column">
                        <h4>Contact Info</h4>
                        <div class="contact-item">
                            <div class="contact-icon">📍</div>
                            <span>Ruhengeri, Rwanda</span>
                        </div>
                        <div class="contact-item">
                            <div class="contact-icon">📞</div>
                            <span>+250 783 076 306</span>
                        </div>
                        <div class="contact-item">
                            <div class="contact-icon">✉️</div>
                            <span><EMAIL></span>
                        </div>
                    </div>

                    <div class="footer-column">
                        <h4>IT Support</h4>
                        <p><strong>IZABAYO JEANLUC SEVERIN</strong></p>
                        <div class="contact-item">
                            <div class="contact-icon">📱</div>
                            <span>0790635888</span>
                        </div>
                        <div class="contact-item">
                            <div class="contact-icon">⏰</div>
                            <span>Mon-Fri, 8AM-5PM</span>
                        </div>
                    </div>
                </div>

                <div class="footer-bottom">
                    <p>&copy; 2024 INES Ruhengeri Digital Hub. All rights reserved.</p>
                </div>
            </div>
        </div>
    </footer>
</body>
</html>

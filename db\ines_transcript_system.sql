-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jun 26, 2025 at 10:33 AM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `ines_transcript_system`
--

-- --------------------------------------------------------

--
-- Table structure for table `audit_log`
--

CREATE TABLE `audit_log` (
  `id` int(11) NOT NULL,
  `table_name` varchar(50) NOT NULL,
  `record_id` int(11) NOT NULL,
  `action` varchar(20) NOT NULL,
  `user_reg_no` varchar(50) DEFAULT NULL,
  `details` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`details`)),
  `timestamp` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `departments`
--

CREATE TABLE `departments` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `faculty` varchar(255) NOT NULL,
  `transcript_fee` decimal(10,2) DEFAULT 750000.00,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `departments`
--

INSERT INTO `departments` (`id`, `name`, `faculty`, `transcript_fee`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'Computer Science', 'Faculty of Sciences and Information Technology', 750000.00, 1, '2025-06-25 20:50:04', '2025-06-25 20:50:04'),
(2, 'Statistics Applied to Economy', 'Faculty of Sciences and Information Technology', 750000.00, 1, '2025-06-25 20:50:04', '2025-06-25 20:50:04'),
(3, 'Cooperatives Management', 'Faculty of Economics Social Sciences and Management', 750000.00, 1, '2025-06-25 20:50:04', '2025-06-25 20:50:04'),
(4, 'Civil Engineering', 'Faculty of Engineering and Technology', 800000.00, 1, '2025-06-25 20:50:04', '2025-06-25 20:50:04'),
(5, 'French and English', 'Faculty of Education', 650000.00, 1, '2025-06-25 20:50:04', '2025-06-25 20:50:04'),
(6, 'Architecture', 'Faculty of Engineering and Technology', 800000.00, 1, '2025-06-25 20:50:04', '2025-06-25 20:50:04'),
(7, 'Water Engineering', 'Faculty of Engineering and Technology', 800000.00, 1, '2025-06-25 20:50:04', '2025-06-25 20:50:04'),
(8, 'Business Administration', 'Faculty of Economics Social Sciences and Management', 700000.00, 1, '2025-06-25 20:50:04', '2025-06-25 20:50:04'),
(9, 'Information Technology', 'Faculty of Sciences and Information Technology', 750000.00, 1, '2025-06-25 20:50:04', '2025-06-25 20:50:04'),
(10, 'Mechanical Engineering', 'Faculty of Engineering and Technology', 800000.00, 1, '2025-06-25 20:50:04', '2025-06-25 20:50:04');

-- --------------------------------------------------------

--
-- Table structure for table `files`
--

CREATE TABLE `files` (
  `id` int(11) NOT NULL,
  `request_id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `filename` varchar(255) NOT NULL,
  `file_type` varchar(50) NOT NULL,
  `file_path` varchar(500) NOT NULL,
  `file_size` int(11) DEFAULT NULL,
  `uploaded_by` varchar(100) NOT NULL,
  `uploaded_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Stand-in structure for view `request_statistics`
-- (See below for the actual view)
--
CREATE TABLE `request_statistics` (
`request_date` date
,`total_requests` bigint(21)
,`paid_requests` bigint(21)
,`pending_requests` bigint(21)
,`total_amount` decimal(32,2)
,`total_paid` decimal(32,2)
,`avg_request_amount` decimal(14,6)
,`unique_students` bigint(21)
);

-- --------------------------------------------------------

--
-- Table structure for table `student_payments`
--

CREATE TABLE `student_payments` (
  `id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `academic_year` varchar(20) NOT NULL,
  `amount_paid` decimal(10,2) NOT NULL,
  `payment_date` date NOT NULL,
  `payment_method` varchar(50) NOT NULL,
  `receipt_number` varchar(100) DEFAULT NULL,
  `payment_status` enum('pending','confirmed','rejected','refunded') DEFAULT 'pending',
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `student_payments`
--

INSERT INTO `student_payments` (`id`, `student_id`, `academic_year`, `amount_paid`, `payment_date`, `payment_method`, `receipt_number`, `payment_status`, `notes`, `created_at`) VALUES
(1, 4, '2020-2021', 1500000.00, '2020-09-15', 'bank_transfer', 'REC001', 'confirmed', 'First year tuition payment', '2025-06-25 20:50:04'),
(2, 4, '2021-2022', 1500000.00, '2021-09-10', 'mobile_money', 'REC002', 'confirmed', 'Second year tuition payment', '2025-06-25 20:50:04'),
(3, 4, '2022-2023', 1500000.00, '2022-09-12', 'mobile_money', 'REC003', 'confirmed', 'Third year tuition payment', '2025-06-25 20:50:04'),
(4, 4, '2023-2024', 1500000.00, '2023-09-08', 'bank_transfer', 'REC004', 'confirmed', 'Fourth year tuition payment', '2025-06-25 20:50:04'),
(5, 5, '2021-2022', 1600000.00, '2021-09-15', 'bank_transfer', 'REC005', 'confirmed', 'First year tuition payment', '2025-06-25 20:50:04'),
(6, 5, '2022-2023', 1600000.00, '2022-09-10', 'mobile_money', 'REC006', 'confirmed', 'Second year tuition payment', '2025-06-25 20:50:04'),
(7, 5, '2023-2024', 1600000.00, '2023-09-12', 'bank_transfer', 'REC007', 'confirmed', 'Third year tuition payment', '2025-06-25 20:50:04'),
(8, 6, '2019-2020', 1400000.00, '2019-09-10', 'cash', 'REC008', 'confirmed', 'First year tuition payment', '2025-06-25 20:50:04'),
(9, 6, '2020-2021', 1400000.00, '2020-09-08', 'mobile_money', 'REC009', 'confirmed', 'Second year tuition payment', '2025-06-25 20:50:04'),
(10, 6, '2021-2022', 1400000.00, '2021-09-15', 'bank_transfer', 'REC010', 'confirmed', 'Third year tuition payment', '2025-06-25 20:50:04');

-- --------------------------------------------------------

--
-- Table structure for table `transcript_requests`
--

CREATE TABLE `transcript_requests` (
  `id` int(11) NOT NULL,
  `student_id` int(11) NOT NULL,
  `request_number` varchar(50) NOT NULL,
  `academic_years` longtext NOT NULL,
  `total_transcripts` int(11) NOT NULL,
  `total_amount` decimal(10,2) NOT NULL,
  `payment_method` varchar(50) DEFAULT NULL,
  `academic_year` varchar(20) DEFAULT NULL,
  `amount_paid` decimal(10,2) DEFAULT 0.00,
  `payment_date` date DEFAULT NULL,
  `receipt_number` varchar(100) DEFAULT NULL,
  `payment_status` enum('pending','paid','partial','failed') DEFAULT 'pending',
  `payment_proof_filename` varchar(255) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `approved_date` datetime DEFAULT NULL,
  `completed_at` timestamp NULL DEFAULT NULL,
  `last_downloaded` timestamp NULL DEFAULT NULL,
  `auto_decision` enum('approved','rejected','pending') DEFAULT NULL,
  `auto_decision_reason` text DEFAULT NULL,
  `auto_processed_at` timestamp NULL DEFAULT NULL,
  `finance_confirmed_at` timestamp NULL DEFAULT NULL,
  `finance_confirmed_by` int(11) DEFAULT NULL,
  `faculty_override` tinyint(1) DEFAULT 0,
  `faculty_override_reason` text DEFAULT NULL,
  `faculty_override_by` int(11) DEFAULT NULL,
  `faculty_override_at` timestamp NULL DEFAULT NULL,
  `payment_confirmed_at` datetime DEFAULT NULL,
  `payment_confirmed_by` varchar(100) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `transcript_requests`
--

INSERT INTO `transcript_requests` (`id`, `student_id`, `request_number`, `academic_years`, `total_transcripts`, `total_amount`, `payment_method`, `academic_year`, `amount_paid`, `payment_date`, `receipt_number`, `payment_status`, `payment_proof_filename`, `notes`, `created_at`, `approved_date`, `completed_at`, `last_downloaded`, `auto_decision`, `auto_decision_reason`, `auto_processed_at`, `finance_confirmed_at`, `finance_confirmed_by`, `faculty_override`, `faculty_override_reason`, `faculty_override_by`, `faculty_override_at`, `payment_confirmed_at`, `payment_confirmed_by`) VALUES
(37, 4, 'REQ-2025-0001', '[\"2020-2021\"]', 1, 1000.00, 'flutterwave', NULL, 0.00, NULL, NULL, 'failed', 'payment_proof_STU001_20250626_102652_Screenshot_2024-01-20_161750.png', 'Transcript request\nREJECTED: school fees not enough, please finish up (by finance user ID: 2 at 2025-06-26 10:30:06)', '2025-06-26 08:26:53', NULL, NULL, NULL, 'rejected', 'school fees not enough, please finish up', '2025-06-26 08:30:06', NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL),
(38, 5, 'REQ-2025-0002', '[\"2023-2024\"]', 1, 1000.00, 'flutterwave', NULL, 0.00, NULL, NULL, 'paid', 'payment_proof_STU002_20250626_102830_Screenshot_2025-03-24_002042.png', 'Transcript request', '2025-06-26 08:28:30', NULL, NULL, NULL, NULL, NULL, NULL, '2025-06-26 08:29:28', 2, 0, NULL, NULL, NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `email` varchar(255) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `role` enum('student','faculty','finance','admin') NOT NULL,
  `reg_no` varchar(50) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `email_verified` tinyint(1) DEFAULT 0,
  `last_login` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `enrollment_start_year` int(11) DEFAULT NULL,
  `program_duration` int(11) DEFAULT NULL,
  `enrollment_years` longtext DEFAULT NULL,
  `current_year_level` int(11) DEFAULT NULL,
  `enrollment_status` enum('active','inactive','graduated','suspended') DEFAULT 'active',
  `graduation_date` date DEFAULT NULL,
  `department_name` varchar(255) DEFAULT NULL,
  `faculty_name` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `email`, `password_hash`, `name`, `role`, `reg_no`, `phone`, `is_active`, `email_verified`, `last_login`, `created_at`, `updated_at`, `enrollment_start_year`, `program_duration`, `enrollment_years`, `current_year_level`, `enrollment_status`, `graduation_date`, `department_name`, `faculty_name`) VALUES
(1, '<EMAIL>', '$2b$12$q0qOW/3/n0RfNNiuCdx9Xe1izJ0Nn5dazBYUUMqzV.wGHm.Phb7Ty', 'System Administrator', 'admin', 'ADMIN001', '+250788000001', 1, 0, '2025-06-25 21:10:15', '2025-06-25 20:50:04', '2025-06-25 21:10:15', NULL, NULL, NULL, NULL, 'active', NULL, NULL, NULL),
(2, '<EMAIL>', '$2b$12$q0qOW/3/n0RfNNiuCdx9Xe1izJ0Nn5dazBYUUMqzV.wGHm.Phb7Ty', 'Finance Office', 'finance', 'FIN001', '+250788000002', 1, 0, '2025-06-26 08:29:01', '2025-06-25 20:50:04', '2025-06-26 08:29:01', NULL, NULL, NULL, NULL, 'active', NULL, NULL, NULL),
(3, '<EMAIL>', '$2b$12$q0qOW/3/n0RfNNiuCdx9Xe1izJ0Nn5dazBYUUMqzV.wGHm.Phb7Ty', 'Faculty Office', 'faculty', 'FAC001', '+250788000003', 1, 0, '2025-06-26 08:32:18', '2025-06-25 20:50:04', '2025-06-26 08:32:18', NULL, NULL, NULL, NULL, 'active', NULL, 'Computer Science', 'Faculty of Sciences and Information Technology'),
(4, '<EMAIL>', '$2b$12$q0qOW/3/n0RfNNiuCdx9Xe1izJ0Nn5dazBYUUMqzV.wGHm.Phb7Ty', 'IZABAYOO Jean Luc', 'student', 'STU001', '+250788000004', 1, 0, '2025-06-26 08:30:38', '2025-06-25 20:50:04', '2025-06-26 08:30:38', 2020, 4, NULL, 4, 'active', NULL, 'Computer Science', 'Faculty of Sciences and Information Technology'),
(5, '<EMAIL>', '$2b$12$q0qOW/3/n0RfNNiuCdx9Xe1izJ0Nn5dazBYUUMqzV.wGHm.Phb7Ty', 'Jane Smith', 'student', 'STU002', '+250788000005', 1, 0, '2025-06-26 08:31:21', '2025-06-25 20:50:04', '2025-06-26 08:31:21', 2021, 4, NULL, 3, 'active', NULL, 'Civil Engineering', 'Faculty of Engineering and Technology'),
(6, '<EMAIL>', '$2b$12$q0qOW/3/n0RfNNiuCdx9Xe1izJ0Nn5dazBYUUMqzV.wGHm.Phb7Ty', 'Alice Johnson', 'student', 'STU003', '+250788000006', 1, 0, NULL, '2025-06-25 20:50:04', '2025-06-25 20:53:30', 2019, 3, NULL, 3, 'graduated', NULL, 'Business Administration', 'Faculty of Economics Social Sciences and Management');

-- --------------------------------------------------------

--
-- Structure for view `request_statistics`
--
DROP TABLE IF EXISTS `request_statistics`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `request_statistics`  AS SELECT cast(`tr`.`created_at` as date) AS `request_date`, count(0) AS `total_requests`, count(case when `tr`.`payment_status` = 'paid' then 1 end) AS `paid_requests`, count(case when `tr`.`payment_status` = 'pending' then 1 end) AS `pending_requests`, sum(`tr`.`total_amount`) AS `total_amount`, sum(case when `tr`.`payment_status` = 'paid' then `tr`.`amount_paid` else 0 end) AS `total_paid`, avg(`tr`.`total_amount`) AS `avg_request_amount`, count(distinct `tr`.`student_id`) AS `unique_students` FROM `transcript_requests` AS `tr` GROUP BY cast(`tr`.`created_at` as date) ORDER BY cast(`tr`.`created_at` as date) DESC ;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `audit_log`
--
ALTER TABLE `audit_log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_table` (`table_name`),
  ADD KEY `idx_user` (`user_reg_no`),
  ADD KEY `idx_timestamp` (`timestamp`),
  ADD KEY `idx_action` (`action`);

--
-- Indexes for table `departments`
--
ALTER TABLE `departments`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `name` (`name`),
  ADD KEY `idx_faculty` (`faculty`),
  ADD KEY `idx_name` (`name`);

--
-- Indexes for table `files`
--
ALTER TABLE `files`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_request` (`request_id`),
  ADD KEY `idx_student` (`student_id`),
  ADD KEY `idx_file_type` (`file_type`),
  ADD KEY `idx_uploaded_at` (`uploaded_at`);

--
-- Indexes for table `student_payments`
--
ALTER TABLE `student_payments`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_student` (`student_id`),
  ADD KEY `idx_academic_year` (`academic_year`),
  ADD KEY `idx_payment_status` (`payment_status`),
  ADD KEY `idx_payment_date` (`payment_date`),
  ADD KEY `idx_receipt_number` (`receipt_number`);

--
-- Indexes for table `transcript_requests`
--
ALTER TABLE `transcript_requests`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `request_number` (`request_number`),
  ADD KEY `finance_confirmed_by` (`finance_confirmed_by`),
  ADD KEY `faculty_override_by` (`faculty_override_by`),
  ADD KEY `idx_student` (`student_id`),
  ADD KEY `idx_request_number` (`request_number`),
  ADD KEY `idx_payment_status` (`payment_status`),
  ADD KEY `idx_auto_decision` (`auto_decision`),
  ADD KEY `idx_created_at` (`created_at`),
  ADD KEY `idx_academic_year` (`academic_year`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`),
  ADD UNIQUE KEY `reg_no` (`reg_no`),
  ADD KEY `idx_email` (`email`),
  ADD KEY `idx_reg_no` (`reg_no`),
  ADD KEY `idx_role` (`role`),
  ADD KEY `idx_department` (`department_name`),
  ADD KEY `idx_faculty` (`faculty_name`),
  ADD KEY `idx_enrollment_status` (`enrollment_status`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `audit_log`
--
ALTER TABLE `audit_log`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `departments`
--
ALTER TABLE `departments`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `files`
--
ALTER TABLE `files`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `student_payments`
--
ALTER TABLE `student_payments`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `transcript_requests`
--
ALTER TABLE `transcript_requests`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=39;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `files`
--
ALTER TABLE `files`
  ADD CONSTRAINT `files_ibfk_1` FOREIGN KEY (`request_id`) REFERENCES `transcript_requests` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `files_ibfk_2` FOREIGN KEY (`student_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `student_payments`
--
ALTER TABLE `student_payments`
  ADD CONSTRAINT `student_payments_ibfk_1` FOREIGN KEY (`student_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `transcript_requests`
--
ALTER TABLE `transcript_requests`
  ADD CONSTRAINT `transcript_requests_ibfk_1` FOREIGN KEY (`student_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `transcript_requests_ibfk_2` FOREIGN KEY (`finance_confirmed_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `transcript_requests_ibfk_3` FOREIGN KEY (`faculty_override_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;

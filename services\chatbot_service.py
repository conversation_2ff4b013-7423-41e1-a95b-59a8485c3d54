"""
Chatbot service for INES Transcript System
Intelligent chatbot responses and help system
"""
import random

def get_chatbot_response(user_message):
    """Generate intelligent chatbot response"""
    message = user_message.lower().strip()
    
    # Greetings
    if any(word in message for word in ['hello', 'hi', 'hey']):
        greetings = [
            "Hello! 👋 I'm here to help with the INES Transcript System. What can I assist you with?",
            "Hi there! 😊 I can help you with login, requests, payments, or any system questions.",
            "Hey! 🌟 I'm your transcript system assistant. How can I help you today?"
        ]
        return random.choice(greetings)
    
    # Login help
    if any(word in message for word in ['login', 'log in', 'sign in']):
        return """🔐 **Login Steps:**
1. Select your role (Student/Finance/Faculty)
2. Choose department (if student/faculty)
3. Enter email and password
4. Click Login

**Demo accounts:**
👨‍🎓 Student: <EMAIL> / password123
💰 Finance: <EMAIL> / finance123
👨‍🏫 Faculty: <EMAIL> / faculty123"""
    
    # Request process
    if any(word in message for word in ['request', 'transcript', 'apply']):
        return """📄 **Request Process:**
1. <PERSON><PERSON> as student
2. Click 'Request Transcript'
3. Select academic years
4. Choose payment method
5. Upload payment proof
6. Track status in dashboard

**Timeline:** 2-5 business days total"""
    
    # Payment info
    if any(word in message for word in ['payment', 'pay', 'cost', 'fee']):
        return """💳 **Payment Information:**
**Methods:**
📱 Mobile Money (MoMo) - No extra fees
💳 Flutterwave (Cards) - Small processing fee

**Typical Fees:**
• Sciences/IT: 750,000 RWF
• Engineering: 800,000 RWF
• Education: 650,000 RWF"""
    
    # Status tracking
    if any(word in message for word in ['status', 'track', 'progress']):
        return """📊 **Track Your Request:**
1. Login to student dashboard
2. Click 'Request Status'
3. Check current progress

**Status Types:**
🟡 Pending Finance (1-2 hours)
🟢 Finance Approved (1-2 days)
🔵 Faculty Processing (2-3 days)
✅ Completed (Download ready)"""
    
    # Download help
    if any(word in message for word in ['download', 'get transcript', 'completed']):
        return """📥 **Download Steps:**
1. Login to student dashboard
2. Go to 'View Downloads'
3. Find completed request
4. Click download button
5. Save PDF file

**Note:** Request status must show 'Completed' ✅"""
    
    # Contact info
    if any(word in message for word in ['contact', 'support', 'help']):
        return """📞 **Contact INES:**
📧 Email: <EMAIL>
📱 Phone: +250 788 123 456
🏢 Address: Musanze, Northern Province
⏰ Hours: Mon-Fri 8AM-5PM

**For technical issues:** Visit Administration Office"""
    
    # Default response
    return """😊 **I can help you with:**

🔐 **Login Process** - Step-by-step guide
📄 **Transcript Requests** - Complete process
💳 **Payment Methods** - MoMo, cards, fees
📊 **Status Tracking** - Monitor progress
📥 **Downloads** - Get completed transcripts
📞 **Contact Info** - Support and offices

**Try asking:**
• "How do I login?"
• "How to request transcript?"
• "What payment methods are available?"
• "How to track my request?"

What would you like to know? 🤔"""
#!/usr/bin/env python3
"""
Test script to verify the payment error fix
"""

import requests
import json
import time

def test_payment_flow():
    """Test the complete payment flow to verify error fix"""
    base_url = "http://localhost:5000"
    
    print("🧪 Testing Payment Flow Error Fix")
    print("=" * 50)
    
    # Create a session
    session = requests.Session()
    
    try:
        # Step 1: Test if server is running
        print("1. Testing server connectivity...")
        response = session.get(base_url)
        if response.status_code == 200:
            print("✅ Server is running")
        else:
            print(f"❌ Server error: {response.status_code}")
            return
            
        # Step 2: Login as student
        print("2. Logging in as student...")
        login_data = {
            'username': 'student1',
            'password': 'password123'
        }
        response = session.post(f"{base_url}/login", data=login_data)
        if response.status_code == 200 and 'student' in response.text.lower():
            print("✅ Student login successful")
        else:
            print(f"❌ Login failed: {response.status_code}")
            return
            
        # Step 3: Navigate to transcript request
        print("3. Accessing transcript request page...")
        response = session.get(f"{base_url}/student/request-transcript")
        if response.status_code == 200:
            print("✅ Transcript request page accessible")
        else:
            print(f"❌ Transcript request page error: {response.status_code}")
            return

        # Step 4: Submit transcript request
        print("4. Submitting transcript request...")
        request_data = {
            'transcript_type': 'official',
            'delivery_method': 'pickup',
            'academic_years': ['2023-2024'],  # This should now work without validation
            'purpose': 'Job Application'
        }
        response = session.post(f"{base_url}/student/request-transcript", data=request_data)
        if response.status_code in [200, 302]:  # 302 for redirect
            print("✅ Transcript request submitted")
        else:
            print(f"❌ Transcript request failed: {response.status_code}")
            return
            
        # Step 5: Access payment page
        print("5. Accessing payment page...")
        response = session.get(f"{base_url}/student/payment")
        if response.status_code == 200:
            print("✅ Payment page accessible")
        else:
            print(f"❌ Payment page error: {response.status_code}")
            return
            
        # Step 6: Test payment submission (this is where the error was occurring)
        print("6. Testing payment submission...")
        payment_data = {
            'payment_method': 'mobile_money',
            'phone': '250788123456'
        }
        response = session.post(f"{base_url}/student/payment", data=payment_data)
        
        print(f"Payment response status: {response.status_code}")
        print(f"Payment response URL: {response.url}")
        
        if response.status_code in [200, 302]:
            if 'payment_status' in response.url or 'payment_status' in response.text:
                print("✅ Payment submission successful - redirected to status page")
                print("🎉 ERROR FIX VERIFIED: Payment flow now works!")
            else:
                print("⚠️  Payment submitted but unexpected redirect")
        else:
            print(f"❌ Payment submission failed: {response.status_code}")
            print(f"Response content: {response.text[:500]}...")
            return
            
        # Step 7: Test payment status page
        print("7. Testing payment status page...")
        response = session.get(f"{base_url}/student/payment_status")
        if response.status_code == 200:
            if 'USSD' in response.text or '*182*7*1#' in response.text:
                print("✅ Payment status page shows USSD instructions")
                print("✅ Manual payment flow is working")
            else:
                print("⚠️  Payment status page accessible but no USSD instructions")
        else:
            print(f"❌ Payment status page error: {response.status_code}")
            
        print("\n" + "=" * 50)
        print("🎯 PAYMENT ERROR FIX TEST COMPLETED")
        print("✅ The error after inputting phone number should now be resolved!")
        print("💡 Users can now complete payments using USSD *182*7*1#")
        
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_payment_flow()

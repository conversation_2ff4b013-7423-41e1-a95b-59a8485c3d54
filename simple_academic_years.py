"""
Simple Academic Years Service
Works with existing simplified database structure
"""

import pymysql
from datetime import datetime
from contextlib import contextmanager

DB_CONFIG = {
    'host': 'localhost',
    'user': 'ines_app',
    'password': 'ines_secure_2025!',
    'database': 'ines_transcript_system',
    'charset': 'utf8mb4'
}

@contextmanager
def get_db_connection():
    connection = pymysql.connect(**DB_CONFIG)
    try:
        yield connection
    finally:
        connection.close()

# Real INES Department program durations (in years)
DEPARTMENT_PROGRAM_DURATIONS = {
    # Faculty of Sciences and Information Technology
    'Computer Science': 3,
    'Statistics Applied to Economy': 3,
    'Computer System Technology': 3,
    'Computer Applications': 3,
    'Domestic Electricity': 3,
    'Master of Science in Software Engineering': 2,
    'Information Sciences and Library Management': 3,
    'Land Management and Valuation': 3,
    
    # Faculty of Engineering and Technology
    'Civil Engineering': 4,
    'Biotechnologies': 4,
    'Land Survey': 3,
    'Architecture': 4,
    'Water Engineering': 4,
    'Masonry': 2,
    'Welding': 2,
    'Domestic Plumbing': 2,
    'Surveying': 3,
    'Master of Science in Food Science and Technology': 2,
    'Master of Science in Geo-Informatics': 2,
    'Master of Science in Construction Technology and Management': 2,
    'Master of Science in Geotechnical Engineering': 2,
    
    # Faculty of Economics, Social Sciences & Management
    'Applied Economics': 3,
    'Enterprises Management': 3,
    'Microfinance': 3,
    'Taxation': 3,
    'Cooperatives Management': 3,
    'Entrepreneurship and SME\'s Management': 3,
    
    # Faculty of Health Sciences
    'Biomedical Laboratory Sciences': 3,
    'Master of Science in Biomedical Laboratory Sciences': 2,
    'Bachelor of Science in Pharmacy': 4,
    'Bachelor of Science in Anaesthesia': 3,
    'Bachelor of Science in Midwifery': 3,
    'Bachelor of Science in General Nursing': 3,
    'Advanced Diploma in Nursing': 3,
    'Advanced Diploma in Midwifery': 3,
    
    # Faculty of Law and Public Administration
    'Law': 4,
    'Public Administration and Good Governance': 3,
    'Master of Applied Criminal Law': 2,
    
    # Faculty of Education
    'French and English': 3
}

def get_department_program_duration(department_name):
    """Get program duration for a department"""
    return DEPARTMENT_PROGRAM_DURATIONS.get(department_name, 3)  # Default to 3 years

def get_current_academic_year():
    """Get current academic year string"""
    return "2025-2026"

def is_current_academic_year(academic_year_str):
    """Check if the given academic year is the current one"""
    current_year = get_current_academic_year()
    return academic_year_str == current_year

def get_student_available_academic_years(student_reg_no):
    """Get available academic years for a student based on their enrollment year"""
    try:
        with get_db_connection() as connection:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # Get student information
                cursor.execute("""
                    SELECT department_name, enrollment_start_year, program_duration
                    FROM users
                    WHERE reg_no = %s AND role = 'student'
                """, (student_reg_no,))
                
                student = cursor.fetchone()
                if not student:
                    return []
                
                department = student['department_name']
                enrollment_year = student.get('enrollment_start_year')
                # Use program_duration from database if available, otherwise use default
                program_duration = student.get('program_duration') or get_department_program_duration(department)
                
                if not enrollment_year:
                    return []  # No enrollment year set
                
                # Program duration already retrieved from database above
                
                # Calculate available years based on enrollment year and current year
                available_years = []
                current_calendar_year = datetime.now().year
                current_academic_year = get_current_academic_year()
                
                # Generate academic years from enrollment year
                for year_offset in range(program_duration):
                    academic_year = enrollment_year + year_offset
                    academic_year_str = f"{academic_year}-{academic_year + 1}"
                    
                    # Only include years that are not the current academic year
                    # and are not in the future
                    if academic_year_str != current_academic_year and academic_year < current_calendar_year:
                        available_years.append(academic_year_str)
                
                return available_years
                
    except Exception as e:
        print(f"Error getting available academic years: {e}")
        return []

def validate_academic_year_request(student_reg_no, requested_year):
    """Validate if student can request transcript for a specific academic year"""
    # Check if it's the current academic year (not allowed)
    if is_current_academic_year(requested_year):
        return False
    
    # Check if it's in the student's available years
    available_years = get_student_available_academic_years(student_reg_no)
    return requested_year in available_years

def get_department_academic_info(department_name):
    """Get academic information for a department"""
    program_duration = get_department_program_duration(department_name)
    
    return {
        'department': department_name,
        'program_duration': program_duration,
        'program_type': f"{program_duration}-year program",
        'typical_years': [f"Year {i}" for i in range(1, program_duration + 1)]
    }

# Compatibility functions for existing code
def get_student_enrollment_years(student_reg_no):
    """Get available academic years (completed only)"""
    return get_student_available_academic_years(student_reg_no)

def validate_student_year_request(student_reg_no, academic_year):
    """Validate year request (compatibility function)"""
    return validate_academic_year_request(student_reg_no, academic_year)
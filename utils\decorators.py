"""
Utility decorators for INES Transcript System
Authentication, authorization, and validation decorators
"""
from functools import wraps
from flask import session, redirect, url_for, flash

def login_required(f):
    """Require user to be logged in"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('Please log in to access this page', 'error')
            return redirect(url_for('auth.login'))
        return f(*args, **kwargs)
    return decorated_function

def role_required(required_role):
    """Require specific user role"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if session.get('role') != required_role:
                flash('Unauthorized access', 'error')
                return redirect(url_for('auth.login'))
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def redirect_if_authenticated(f):
    """Redirect authenticated users away from login/register pages"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' in session:
            role = session.get('role')
            if role == 'student':
                return redirect(url_for('student.dashboard'))
            elif role == 'finance':
                return redirect(url_for('finance.dashboard'))
            elif role == 'faculty':
                return redirect(url_for('faculty.dashboard'))
        return f(*args, **kwargs)
    return decorated_function

# Convenience decorators
student_required = role_required('student')
finance_required = role_required('finance')
faculty_required = role_required('faculty')
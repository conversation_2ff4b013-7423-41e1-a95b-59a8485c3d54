#!/usr/bin/env python3
"""
Detailed Intouch API Testing Script
Tests various endpoints and authentication methods
"""

import requests
import json
import time
from urllib.parse import urljoin

class IntouchAPITester:
    def __init__(self):
        # Production credentials from user
        self.username = 'testa'
        self.password = '+$J<wtZktTDs&-Mk("h5='
        self.account_no = '************'
        
        # Possible base URLs to test
        self.base_urls = [
            'https://www.intouchpay.co.rw/api',
            'https://intouchpay.co.rw/api',
            'https://api.intouchpay.co.rw',
            'https://www.intouchpay.co.rw',
            'https://intouchpay.co.rw'
        ]
        
        # Common headers
        self.headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent': 'INES-Transcript-System/1.0'
        }
    
    def test_connectivity(self):
        """Test basic connectivity to Intouch domains"""
        print("🌐 Testing Basic Connectivity")
        print("=" * 50)
        
        for base_url in self.base_urls:
            try:
                print(f"Testing: {base_url}")
                response = requests.get(base_url, timeout=10, headers=self.headers)
                print(f"   Status: {response.status_code}")
                print(f"   Headers: {dict(response.headers)}")
                if response.text:
                    print(f"   Content: {response.text[:200]}...")
                print()
            except Exception as e:
                print(f"   ❌ Error: {e}")
                print()
    
    def test_login_endpoints(self):
        """Test various login endpoint variations"""
        print("🔐 Testing Login Endpoints")
        print("=" * 50)
        
        login_endpoints = [
            '/login',
            '/auth/login',
            '/api/login',
            '/v1/login',
            '/authenticate',
            '/token'
        ]
        
        # Test different authentication payloads
        auth_payloads = [
            {
                'username': self.username,
                'password': self.password,
                'account_no': self.account_no
            },
            {
                'username': self.username,
                'password': self.password
            },
            {
                'email': self.username,
                'password': self.password,
                'account_no': self.account_no
            },
            {
                'user': self.username,
                'pass': self.password,
                'account': self.account_no
            }
        ]
        
        for base_url in self.base_urls:
            print(f"\n🏠 Testing base URL: {base_url}")
            
            for endpoint in login_endpoints:
                full_url = urljoin(base_url, endpoint)
                print(f"\n   📍 Endpoint: {full_url}")
                
                for i, payload in enumerate(auth_payloads, 1):
                    try:
                        print(f"      Payload {i}: {json.dumps(payload, indent=2)}")
                        
                        response = requests.post(
                            full_url,
                            json=payload,
                            headers=self.headers,
                            timeout=15
                        )
                        
                        print(f"      Status: {response.status_code}")
                        print(f"      Response: {response.text[:300]}")
                        
                        if response.status_code == 200:
                            print("      ✅ SUCCESS!")
                            return response.json(), full_url
                        
                    except Exception as e:
                        print(f"      ❌ Error: {e}")
                
                print("   " + "-" * 40)
        
        return None, None
    
    def test_api_documentation(self):
        """Try to find API documentation or endpoints"""
        print("📚 Looking for API Documentation")
        print("=" * 50)
        
        doc_endpoints = [
            '/docs',
            '/api/docs',
            '/swagger',
            '/api-docs',
            '/documentation',
            '/help',
            '/endpoints',
            '/v1',
            '/api/v1'
        ]
        
        for base_url in self.base_urls:
            for endpoint in doc_endpoints:
                try:
                    full_url = urljoin(base_url, endpoint)
                    response = requests.get(full_url, timeout=10, headers=self.headers)
                    
                    if response.status_code == 200:
                        print(f"✅ Found: {full_url}")
                        print(f"   Content: {response.text[:500]}...")
                        print()
                except:
                    pass
    
    def test_alternative_methods(self):
        """Test alternative authentication methods"""
        print("🔄 Testing Alternative Methods")
        print("=" * 50)
        
        # Try with form data instead of JSON
        form_data = {
            'username': self.username,
            'password': self.password,
            'account_no': self.account_no
        }
        
        # Try with different headers
        alt_headers = [
            {'Content-Type': 'application/x-www-form-urlencoded'},
            {'Content-Type': 'application/json', 'X-API-Key': 'test'},
            {'Content-Type': 'application/json', 'Authorization': 'Bearer test'}
        ]
        
        for base_url in ['https://www.intouchpay.co.rw/api']:
            for headers in alt_headers:
                try:
                    print(f"Testing with headers: {headers}")
                    
                    if 'form-urlencoded' in headers.get('Content-Type', ''):
                        response = requests.post(
                            f"{base_url}/login",
                            data=form_data,
                            headers=headers,
                            timeout=15
                        )
                    else:
                        response = requests.post(
                            f"{base_url}/login",
                            json=form_data,
                            headers=headers,
                            timeout=15
                        )
                    
                    print(f"   Status: {response.status_code}")
                    print(f"   Response: {response.text[:200]}")
                    print()
                    
                except Exception as e:
                    print(f"   Error: {e}")
                    print()

def main():
    print("🧪 COMPREHENSIVE INTOUCH API TESTING")
    print("=" * 60)
    print("Testing with production credentials:")
    print("Username: testa")
    print("Account: ************")
    print("Password: [HIDDEN]")
    print("=" * 60)
    
    tester = IntouchAPITester()
    
    # Test 1: Basic connectivity
    tester.test_connectivity()
    
    # Test 2: Login endpoints
    auth_result, working_url = tester.test_login_endpoints()
    
    if auth_result:
        print("🎉 AUTHENTICATION SUCCESSFUL!")
        print(f"Working URL: {working_url}")
        print(f"Response: {json.dumps(auth_result, indent=2)}")
    else:
        print("❌ No working authentication found")
    
    # Test 3: API documentation
    tester.test_api_documentation()
    
    # Test 4: Alternative methods
    tester.test_alternative_methods()
    
    print("\n" + "=" * 60)
    print("🏁 TESTING COMPLETE")
    
    if auth_result:
        print("✅ Ready to implement working authentication")
    else:
        print("❌ Need to investigate further or contact Intouch support")

if __name__ == "__main__":
    main()

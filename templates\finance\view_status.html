<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View Status | INES-Ruhengeri Transcript System</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .sidebar-header {
            text-align: center;
            padding: 20px;
            border-bottom: 1px solid #e0e0e0;
        }
        .profile-image {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #007bff;
            margin-bottom: 15px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .sidebar-header h2 {
            color: #fff;
            font-size: 1.2rem;
            margin: 10px 0 5px;
            font-weight: 600;
        }
        .sidebar-header p {
            color: #fff;
            font-size: 0.9rem;
            margin: 0;
        }
        .dashboard-header {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            text-align: center;
        }
        .dashboard-header h1 {
            color: #333;
            font-size: 2rem;
            margin-bottom: 10px;
            font-weight: 700;
        }
        .dashboard-header p {
            color: #666;
            font-size: 1.1rem;
            margin: 0;
        }
        .category-selection {
            margin-bottom: 30px;
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .category-selection h3 {
            color: #333;
            font-size: 1.3rem;
            margin-bottom: 20px;
            font-weight: 600;
        }
        .category-btn {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            color: #495057;
            padding: 18px 30px;
            margin: 0 15px 15px 0;
            border-radius: 12px;
            text-decoration: none;
            display: inline-block;
            font-weight: 600;
            transition: all 0.3s ease;
            font-size: 1rem;
            min-width: 180px;
            text-align: center;
        }
        .category-btn:hover {
            background: #e9ecef;
            border-color: #007bff;
            color: #007bff;
            text-decoration: none;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,123,255,0.2);
        }
        .category-btn.active {
            background: #007bff;
            border-color: #007bff;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,123,255,0.3);
        }
        .category-btn i {
            margin-right: 8px;
            font-size: 1.1rem;
        }
        .confirm-btn {
            background: #28a745;
            border: none;
            color: white;
            padding: 5px 15px;
            border-radius: 4px;
            font-size: 0.875rem;
            margin-right: 5px;
        }
        .confirm-btn:hover {
            background: #218838;
        }
        .auto-timer-notice {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 10px 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-size: 0.9rem;
        }
        .badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
            text-align: center;
            white-space: nowrap;
            vertical-align: baseline;
        }
        .auto-decision-info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            color: #0056b3;
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        .auto-decision-info i {
            margin-right: 8px;
        }
        .modal {
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        .modal-content {
            background-color: #fefefe;
            margin: 15% auto;
            padding: 20px;
            border: 1px solid #888;
            width: 400px;
            border-radius: 8px;
        }
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        .close:hover {
            color: black;
        }
        .modal-footer {
            margin-top: 20px;
            text-align: right;
        }
        .modal-footer button {
            margin-left: 10px;
        }
        .card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            overflow: hidden;
        }
        .card-header {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 20px 25px;
            border-bottom: none;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .card-header h2 {
            margin: 0;
            font-size: 1.4rem;
            font-weight: 600;
        }
        .card-body {
            padding: 25px;
        }
        .table-responsive {
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            font-size: 0.95rem;
        }
        table th {
            background: #f8f9fa;
            color: #333;
            padding: 15px 12px;
            text-align: left;
            font-weight: 600;
            border-bottom: 2px solid #e9ecef;
            font-size: 0.9rem;
        }
        table td {
            padding: 15px 12px;
            border-bottom: 1px solid #e9ecef;
            color: #495057;
            vertical-align: middle;
        }
        table tr:hover {
            background: #f8f9fa;
        }
        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .status-completed {
            background: #d4edda;
            color: #155724;
        }
        .status-approved {
            background: #cce5ff;
            color: #004085;
        }
        .status-rejected {
            background: #f8d7da;
            color: #721c24;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        .btn-sm {
            padding: 6px 12px;
            font-size: 0.8rem;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background: #0056b3;
        }
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        .btn-danger:hover {
            background: #c82333;
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background: #545b62;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="{{ url_for('static', filename='images/user.jpeg') }}" alt="Finance Profile" class="profile-image">
                <h2>{{ session.name }}</h2>
                <p>Finance ID: {{ session.user_id }}</p>
            </div>
            
            <nav class="sidebar-menu">
                <ul>
                    <li>
                        <a href="{{ url_for('finance_dashboard') }}">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('view_status') }}" class="active">
                            <i class="fas fa-eye"></i> View Status
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('view_history') }}">
                            <i class="fas fa-history"></i> View History
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('manage_fees') }}">
                            <i class="fas fa-money-bill-wave"></i> Manage Fees
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('logout') }}">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>
        
        <!-- Main Content -->
        <main class="main-content">
            <div class="top-bar">
                <button class="menu-toggle" id="menu-toggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }}">
                            <i class="fas fa-{% if category == 'success' %}check-circle{% elif category == 'error' %}exclamation-circle{% elif category == 'warning' %}exclamation-triangle{% else %}info-circle{% endif %}"></i>
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
            
            <div class="dashboard-header">
                <h1>Request Status Management</h1>
                <p>View and manage transcript request statuses</p>
            </div>



            <!-- Category Selection -->
            <div class="category-selection">
                <h3>Select a category to view transcript requests:</h3>
                <div>
                    <a href="{{ url_for('view_status', category='approved') }}"
                       class="category-btn {{ 'active' if category == 'approved' else '' }}">
                        <i class="fas fa-check-circle"></i> Approved Requests
                        {% if approved_count > 0 %}
                            <span style="background: #28a745; color: white; padding: 2px 6px; border-radius: 10px; font-size: 0.7rem; margin-left: 5px;">{{ approved_count }}</span>
                        {% endif %}
                    </a>
                    <a href="{{ url_for('view_status', category='rejected') }}"
                       class="category-btn {{ 'active' if category == 'rejected' else '' }}">
                        <i class="fas fa-times-circle"></i> Rejected Requests
                        {% if rejected_count > 0 %}
                            <span style="background: #dc3545; color: white; padding: 2px 6px; border-radius: 10px; font-size: 0.7rem; margin-left: 5px;">{{ rejected_count }}</span>
                        {% endif %}
                    </a>
                    <a href="{{ url_for('view_status', category='history') }}"
                       class="category-btn {{ 'active' if category == 'history' else '' }}">
                        <i class="fas fa-history"></i> View History
                    </a>
                </div>
                <p style="color: #666; font-size: 0.9rem; margin-top: 10px;">
                    Please select a category above to view the corresponding requests.
                </p>
            </div>

            {% if category %}
                {% if category == 'approved' %}
                    <!-- Approved Requests -->
                    <div class="card">
                        <div class="card-header">
                            <h2><i class="fas fa-check-circle"></i> Approved Requests ({{ approved_count }})</h2>
                        </div>
                        <div class="card-body">
                            {% if approved_requests %}
                                <div class="table-responsive">
                                    <table>
                                        <thead>
                                            <tr>
                                                <th>Request ID</th>
                                                <th>Student Name</th>
                                                <th>Student ID</th>
                                                <th>Academic Years</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for request in approved_requests %}
                                            <tr id="request-{{ request.id }}">
                                                <td><strong>{{ request.id }}</strong></td>
                                                <td>{{ request.student_name }}</td>
                                                <td>{{ request.student_id }}</td>
                                                <td>{{ ', '.join(request.academic_years) }}</td>
                                                <td>
                                                    {% if request.status == 'completed' %}
                                                        <img src="{{ url_for('static', filename='images/8.png') }}" alt="Completed" style="width: 24px; height: 24px;">
                                                    {% elif request.status == 'approved_finance' %}
                                                        <img src="{{ url_for('static', filename='images/approved.png') }}" alt="Approved" style="width: 24px; height: 24px;">
                                                    {% else %}
                                                        <img src="{{ url_for('static', filename='images/approved.png') }}" alt="Approved" style="width: 24px; height: 24px;">
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    {% if request.finance_confirmed_at %}
                                                        <button type="button" class="confirm-btn"
                                                                style="background: #28a745; cursor: default;"
                                                                data-confirmed="true">
                                                            <i class="fas fa-check-circle"></i> Done
                                                        </button>
                                                    {% else %}
                                                        <button type="button" class="confirm-btn"
                                                                onclick="confirmRequest({{ request.id }}, 'approved')"
                                                                data-confirmed="false">
                                                            <i class="fas fa-check"></i> Confirm
                                                        </button>
                                                    {% endif %}
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            {% else %}
                                <div style="text-align: center; padding: 40px;">
                                    <i class="fas fa-inbox" style="font-size: 3rem; color: #ccc; margin-bottom: 15px;"></i>
                                    <p style="color: #666;">No approved requests found.</p>
                                </div>
                            {% endif %}
                        </div>
                    </div>

                {% elif category == 'rejected' %}
                    <!-- Rejected Requests -->
                    <div class="card">
                        <div class="card-header">
                            <h2><i class="fas fa-times-circle"></i> Rejected Requests ({{ rejected_count }})</h2>
                        </div>
                        <div class="card-body">
                            {% if rejected_requests %}
                                <div class="table-responsive">
                                    <table>
                                        <thead>
                                            <tr>
                                                <th>Request ID</th>
                                                <th>Student Name</th>
                                                <th>Student ID</th>
                                                <th>Academic Years</th>
                                                <th>Status</th>
                                                <th>Reason for Rejection</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for request in rejected_requests %}
                                            <tr id="request-{{ request.id }}">
                                                <td><strong>{{ request.id }}</strong></td>
                                                <td>{{ request.student_name }}</td>
                                                <td>{{ request.student_id }}</td>
                                                <td>{{ ', '.join(request.academic_years) }}</td>
                                                <td>
                                                    <img src="{{ url_for('static', filename='images/rejected.png') }}" alt="Rejected" style="width: 24px; height: 24px;">
                                                </td>
                                                <td>
                                                    <small style="color: #666;" title="{{ request.rejection_reason }}">
                                                        {{ request.rejection_reason[:80] }}{% if request.rejection_reason|length > 80 %}...{% endif %}
                                                    </small>
                                                </td>
                                                <td>
                                                    {% if request.finance_confirmed_at %}
                                                        <button type="button" class="confirm-btn"
                                                                style="background: #28a745; cursor: default;"
                                                                data-confirmed="true">
                                                            <i class="fas fa-check-circle"></i> Done
                                                        </button>
                                                    {% else %}
                                                        <button type="button" class="confirm-btn"
                                                                onclick="confirmRequest({{ request.id }}, 'rejected')"
                                                                data-confirmed="false">
                                                            <i class="fas fa-check"></i> Confirm
                                                        </button>
                                                    {% endif %}
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            {% else %}
                                <div style="text-align: center; padding: 40px;">
                                    <i class="fas fa-inbox" style="font-size: 3rem; color: #ccc; margin-bottom: 15px;"></i>
                                    <p style="color: #666;">No rejected requests found.</p>
                                </div>
                            {% endif %}
                        </div>
                    </div>

                {% elif category == 'history' %}
                    <!-- Combined History View -->
                    <div class="card">
                        <div class="card-header">
                            <h2><i class="fas fa-history"></i> Request History</h2>
                            <button type="button" class="btn btn-sm btn-primary" onclick="window.print()">
                                <i class="fas fa-print"></i> Print Report
                            </button>
                        </div>
                        <div class="card-body">
                            {% if approved_requests or rejected_requests %}
                                <div class="table-responsive">
                                    <table>
                                        <thead>
                                            <tr>
                                                <th>Request ID</th>
                                                <th>Student Name</th>
                                                <th>Student ID</th>
                                                <th>Academic Years</th>
                                                <th>Status</th>
                                                <th>Reason/Notes</th>
                                                <th>Date Processed</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for request in approved_requests + rejected_requests %}
                                            <tr>
                                                <td><strong>{{ request.id }}</strong></td>
                                                <td>{{ request.student_name }}</td>
                                                <td>{{ request.student_id }}</td>
                                                <td>{{ ', '.join(request.academic_years) }}</td>
                                                <td>
                                                    {% if request.status == 'completed' %}
                                                        <span class="status-badge status-completed">✓ Completed</span>
                                                    {% elif request.status in ['approved_finance'] %}
                                                        <span class="status-badge status-approved">✓ Approved</span>
                                                    {% else %}
                                                        <span class="status-badge status-rejected">✗ Rejected</span>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    {% if request.status == 'rejected' %}
                                                        <small style="color: #666;">{{ request.rejection_reason[:60] }}{% if request.rejection_reason|length > 60 %}...{% endif %}</small>
                                                    {% elif request.status == 'completed' %}
                                                        <small style="color: #28a745;">Transcript ready for download</small>
                                                    {% else %}
                                                        <small style="color: #007bff;">Awaiting faculty processing</small>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    <small style="color: #666;">
                                                        {{ request.get('date', 'N/A') }}
                                                    </small>
                                                </td>
                                                <td>
                                                    <form method="POST" action="{{ url_for('delete_request_from_history', request_id=request.id) }}" 
                                                          style="display: inline;" 
                                                          onsubmit="return confirm('Are you sure you want to delete this request permanently?')">
                                                        <button type="submit" class="btn btn-sm btn-danger">
                                                            <i class="fas fa-trash"></i> Delete
                                                        </button>
                                                    </form>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            {% else %}
                                <div style="text-align: center; padding: 40px;">
                                    <i class="fas fa-inbox" style="font-size: 3rem; color: #ccc; margin-bottom: 15px;"></i>
                                    <p style="color: #666;">No request history found.</p>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                {% endif %}

            {% else %}
                <!-- Default view - no category selected -->
                <div style="text-align: center; padding: 60px;">
                    <i class="fas fa-mouse-pointer" style="font-size: 4rem; color: #ccc; margin-bottom: 20px;"></i>
                    <h3 style="color: #666;">Select a Category</h3>
                    <p style="color: #999;">Please select a category above to view the corresponding requests.</p>
                </div>
            {% endif %}

        </main>
    </div>

    <!-- Confirmation Modal -->
    <div class="modal" id="confirmModal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Confirm Request</h3>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to confirm this request?</p>
                <div id="confirmDetails"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeModal()">No</button>
                <button type="button" class="btn btn-primary" id="confirmYes">Yes</button>
            </div>
        </div>
    </div>

    <script>
        // Toggle sidebar on mobile
        const menuToggle = document.getElementById('menu-toggle');
        const sidebar = document.querySelector('.sidebar');
        
        menuToggle.addEventListener('click', function() {
            sidebar.classList.toggle('active');
        });
        
        // Close alerts after 5 seconds
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(alert => {
            setTimeout(() => {
                alert.style.display = 'none';
            }, 5000);
        });

        // Confirmation functionality
        let currentRequestId = null;
        let currentRequestType = null;

        function confirmRequest(requestId, requestType) {
            // Check if request is already confirmed
            const requestRow = document.getElementById('request-' + requestId);
            if (requestRow && requestRow.classList.contains('confirmed')) {
                alert('This request has already been confirmed.');
                return;
            }

            currentRequestId = requestId;
            currentRequestType = requestType;

            const modal = document.getElementById('confirmModal');
            const detailsDiv = document.getElementById('confirmDetails');

            if (requestType === 'approved') {
                detailsDiv.innerHTML = '<p style="color: #28a745;">This will send the request to faculty for transcript preparation.</p>';
            } else {
                detailsDiv.innerHTML = '<p style="color: #dc3545;">This will notify the student about the rejection and send them the reason.</p>';
            }

            modal.style.display = 'block';
        }

        function closeModal() {
            document.getElementById('confirmModal').style.display = 'none';
        }

        document.getElementById('confirmYes').addEventListener('click', function() {
            if (currentRequestId) {
                // Mark request as confirmed immediately to prevent double confirmation
                const requestRow = document.getElementById('request-' + currentRequestId);
                if (requestRow) {
                    requestRow.classList.add('confirmed');

                    // Update the confirm button to show "Done" status
                    const confirmBtn = requestRow.querySelector('.confirm-btn');
                    if (confirmBtn) {
                        confirmBtn.innerHTML = '<i class="fas fa-check-circle"></i> Done';
                        confirmBtn.style.backgroundColor = '#28a745';
                        confirmBtn.style.cursor = 'default';
                        confirmBtn.onclick = null; // Remove click handler
                    }
                }

                // Submit confirmation
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `/finance/confirm-request/${currentRequestId}`;
                document.body.appendChild(form);
                form.submit();
            }

            closeModal();
        });

        // Check for already confirmed requests on page load
        document.addEventListener('DOMContentLoaded', function() {
            const requestRows = document.querySelectorAll('[id^="request-"]');
            requestRows.forEach(function(row) {
                // Check if request has finance_confirmed_at timestamp
                const confirmBtn = row.querySelector('.confirm-btn');
                if (confirmBtn && confirmBtn.dataset.confirmed === 'true') {
                    row.classList.add('confirmed');
                    confirmBtn.innerHTML = '<i class="fas fa-check-circle"></i> Done';
                    confirmBtn.style.backgroundColor = '#28a745';
                    confirmBtn.style.cursor = 'default';
                    confirmBtn.onclick = null;
                }
            });
        });

        // Print functionality
        window.addEventListener('beforeprint', function() {
            document.querySelectorAll('.confirm-btn').forEach(btn => btn.style.display = 'none');
            document.querySelectorAll('.btn-danger').forEach(btn => btn.style.display = 'none');
        });
        
        window.addEventListener('afterprint', function() {
            document.querySelectorAll('.confirm-btn').forEach(btn => btn.style.display = 'inline-block');
            document.querySelectorAll('.btn-danger').forEach(btn => btn.style.display = 'inline-block');
        });

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('confirmModal');
            if (event.target == modal) {
                closeModal();
            }
        }
    </script>
</body>
</html>

{% extends "base.html" %}

{% block title %}Approve Requests - Finance{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2 d-md-block sidebar">
            <!-- User Info Section -->
            <div class="sidebar-header">
                <img src="{{ url_for('static', filename='images/user.jpeg') }}" alt="Finance Profile" class="profile-image">
                <h2>{{ session.name }}</h2>
                <p>Finance ID: {{ session.user_id }}</p>
            </div>
            
            <nav class="sidebar-menu">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('finance_dashboard') }}">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ url_for('approve_requests') }}">
                            <i class="fas fa-check-circle"></i> Approve Requests
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('view_status') }}">
                            <i class="fas fa-history"></i> View Status
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('manage_fees') }}">
                            <i class="fas fa-money-bill-wave"></i> Manage Fees
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('logout') }}">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </li>
                </ul>
            </nav>
        </div>

        <!-- Main Content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Approve Requests</h1>
            </div>

            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                            <i class="fas fa-{% if category == 'success' %}check-circle{% elif category == 'error' %}exclamation-circle{% elif category == 'warning' %}exclamation-triangle{% else %}info-circle{% endif %}"></i>
                            {{ message }}
                             <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <div class="card">
                <div class="card-header">
                    Pending Requests
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Request ID</th>
                                    <th>Student Name</th>
                                    <th>Student ID</th>
                                    <th>Academic Years</th>
                                    <th>Total Price</th>
                                    <th>Payment Method</th>
                                    <th>Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for request in requests %}
                                <tr>
                                    <td>{{ request.id }}</td>
                                    <td>{{ request.student_name }}</td>
                                    <td>{{ request.student_id }}</td>
                                    <td>{{ request.academic_years|join(', ') }}</td>
                                    <td>{{ request.total_price }} RWF</td>
                                    <td>{{ request.payment_method }}</td>
                                    <td>{{ request.date }}</td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn btn-success btn-sm" onclick="showApproveModal('{{ request.id }}', '{{ request.student_name }}', '{{ request.date }}')">
                                                <i class="fas fa-check"></i> Approve
                                            </button>
                                            <button class="btn btn-danger btn-sm" onclick="showRejectModal('{{ request.id }}', '{{ request.student_name }}')">
                                                <i class="fas fa-times"></i> Reject
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- Approve Modal -->
<div class="modal fade" id="approveModal" tabindex="-1" aria-labelledby="approveModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="approveModalLabel">Confirm Approval</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="approveForm" method="POST">
                <input type="hidden" name="action" value="approve">
                <div class="modal-body">
                    <p id="approveConfirmationText"></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">Confirm Approval</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Reject Modal -->
<div class="modal fade" id="rejectModal" tabindex="-1" aria-labelledby="rejectModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="rejectModalLabel">Reject Request</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="rejectForm" method="POST">
                <input type="hidden" name="action" value="reject">
                <div class="modal-body">
                    <p id="rejectConfirmationText"></p>
                    <div class="mb-3">
                        <label for="rejectionReason" class="form-label">Reason for Rejection:</label>
                        <textarea class="form-control" id="rejectionReason" name="reason" rows="4" required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Confirm Rejection</button>
                </div>
            </form>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
    // JavaScript for modals using Bootstrap
    function showApproveModal(requestId, studentName, requestDate) {
        const approveModal = new bootstrap.Modal(document.getElementById('approveModal'));
        document.getElementById('approveConfirmationText').textContent = `Are you sure you want to approve the request for ${studentName} that was submitted on ${requestDate}?`;
        document.getElementById('approveForm').action = `/finance/approve/${requestId}`;
        approveModal.show();
    }

    function showRejectModal(requestId, studentName) {
        const rejectModal = new bootstrap.Modal(document.getElementById('rejectModal'));
        document.getElementById('rejectConfirmationText').textContent = `Please provide a reason for rejecting the request for ${studentName}:`;
        document.getElementById('rejectForm').action = `/finance/approve/${requestId}`;
        rejectModal.show();
    }
</script>
{% endblock %}

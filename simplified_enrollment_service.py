"""
Simplified Enrollment Service
Uses enrollment_years field directly from users table
"""

import pymysql
import json

# Database connection configuration
DB_CONFIG = {
    'host': 'localhost',
    'user': 'ines_app',
    'password': 'ines_secure_2025!',
    'database': 'ines_transcript_system',
    'charset': 'utf8mb4'
}

def get_db_connection():
    """Get database connection"""
    return pymysql.connect(**DB_CONFIG)

def get_student_enrollment_years(student_reg_no):
    """Get enrollment years for a student from users table"""
    try:
        connection = get_db_connection()
        
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT enrollment_years FROM new_users 
                WHERE reg_no = %s AND role = 'student'
            """, (student_reg_no,))
            
            result = cursor.fetchone()
            if result and result[0]:
                return json.loads(result[0])
            return []
            
    except Exception as e:
        print(f"Error getting enrollment years: {e}")
        return []
    finally:
        connection.close()

def validate_student_year_request(student_reg_no, requested_year):
    """Validate if student can request transcript for a specific year"""
    try:
        # Convert year format if needed
        if isinstance(requested_year, str):
            if '-' in requested_year:
                year = int(requested_year.split('-')[0])
            else:
                year = int(requested_year)
        else:
            year = requested_year
        
        enrolled_years = get_student_enrollment_years(student_reg_no)
        return year in enrolled_years
        
    except Exception as e:
        print(f"Error validating year request: {e}")
        return False

def get_student_enrollment_info(student_reg_no):
    """Get student enrollment information from users table"""
    try:
        connection = get_db_connection()
        
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            cursor.execute("""
                SELECT reg_no, name, department_name, faculty_name,
                       enrollment_start_year, program_duration, enrollment_years,
                       current_year_level, enrollment_status, graduation_date
                FROM new_users 
                WHERE reg_no = %s AND role = 'student'
            """, (student_reg_no,))
            
            return cursor.fetchone()
            
    except Exception as e:
        print(f"Error getting enrollment info: {e}")
        return None
    finally:
        connection.close()

def update_student_enrollment_years(student_reg_no, new_years):
    """Update enrollment years for a student"""
    try:
        connection = get_db_connection()
        
        with connection.cursor() as cursor:
            cursor.execute("""
                UPDATE new_users 
                SET enrollment_years = %s
                WHERE reg_no = %s AND role = 'student'
            """, (json.dumps(new_years), student_reg_no))
            
            connection.commit()
            return cursor.rowcount > 0
            
    except Exception as e:
        print(f"Error updating enrollment years: {e}")
        return False
    finally:
        connection.close()

def add_enrollment_year(student_reg_no, year):
    """Add an enrollment year to a student"""
    current_years = get_student_enrollment_years(student_reg_no)
    if year not in current_years:
        current_years.append(year)
        current_years.sort()
        return update_student_enrollment_years(student_reg_no, current_years)
    return True

def remove_enrollment_year(student_reg_no, year):
    """Remove an enrollment year from a student"""
    current_years = get_student_enrollment_years(student_reg_no)
    if year in current_years:
        current_years.remove(year)
        return update_student_enrollment_years(student_reg_no, current_years)
    return True

# Wrapper functions for compatibility
def get_student_available_years(student_id):
    """Compatibility wrapper"""
    return get_student_enrollment_years(student_id)

def get_student_enrollment_details(student_id):
    """Compatibility wrapper"""
    return get_student_enrollment_info(student_id)

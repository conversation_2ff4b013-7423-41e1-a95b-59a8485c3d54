#!/usr/bin/env python3
"""
Debug script to diagnose login issues
Tests database connection, user authentication, and password verification
"""

import pymysql
import bcrypt
from simple_database_service import get_db_connection, authenticate_user

def test_database_connection():
    """Test basic database connection"""
    print("🔍 Testing database connection...")
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM users")
            count = cursor.fetchone()[0]
            print(f"✅ Database connection successful. Found {count} users.")
            return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def examine_user_data():
    """Examine user data in the database"""
    print("\n🔍 Examining user data...")
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor(pymysql.cursors.DictCursor)
            cursor.execute("SELECT id, email, role, name, reg_no, is_active, department_name, faculty_name FROM users")
            users = cursor.fetchall()
            
            print(f"Found {len(users)} users:")
            for user in users:
                print(f"  - ID: {user['id']}, Email: {user['email']}, Role: {user['role']}, Active: {user['is_active']}")
                print(f"    Name: {user['name']}, Reg No: {user['reg_no']}")
                print(f"    Department: {user.get('department_name', 'N/A')}, Faculty: {user.get('faculty_name', 'N/A')}")
                print()
            return users
    except Exception as e:
        print(f"❌ Error examining user data: {e}")
        return []

def test_password_verification():
    """Test password verification for known users"""
    print("🔍 Testing password verification...")
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor(pymysql.cursors.DictCursor)
            cursor.execute("SELECT email, password_hash, role FROM users WHERE email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>')")
            users = cursor.fetchall()
            
            # Test password for each user (assuming default password is 'password123')
            test_passwords = ['password123', 'admin123', 'finance123', 'faculty123', '123456', 'password']
            
            for user in users:
                print(f"\nTesting passwords for {user['email']} ({user['role']}):")
                for test_password in test_passwords:
                    try:
                        if bcrypt.checkpw(test_password.encode('utf-8'), user['password_hash'].encode('utf-8')):
                            print(f"  ✅ Password '{test_password}' works!")
                            break
                    except Exception as e:
                        print(f"  ❌ Error testing password '{test_password}': {e}")
                else:
                    print(f"  ❌ None of the test passwords work for {user['email']}")
                    
    except Exception as e:
        print(f"❌ Error testing password verification: {e}")

def test_authentication_function():
    """Test the authenticate_user function directly"""
    print("\n🔍 Testing authenticate_user function...")
    
    test_cases = [
        # Student login
        {
            'email': '<EMAIL>',
            'password': 'password123',
            'department': 'Computer Science',
            'role': 'student'
        },
        # Finance login
        {
            'email': '<EMAIL>',
            'password': 'password123',
            'department': None,
            'role': 'finance'
        },
        # Faculty login
        {
            'email': '<EMAIL>',
            'password': 'password123',
            'department': 'Faculty of Sciences and Information Technology',
            'role': 'faculty'
        }
    ]
    
    for test_case in test_cases:
        print(f"\nTesting login for {test_case['email']} as {test_case['role']}:")
        result = authenticate_user(
            test_case['email'],
            test_case['password'],
            test_case['department'],
            test_case['role']
        )
        
        if result:
            print(f"  ✅ Authentication successful!")
            print(f"  User data: {result}")
        else:
            print(f"  ❌ Authentication failed!")

def check_password_hashes():
    """Check if password hashes are properly formatted"""
    print("\n🔍 Checking password hash formats...")
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor(pymysql.cursors.DictCursor)
            cursor.execute("SELECT email, password_hash FROM users LIMIT 3")
            users = cursor.fetchall()
            
            for user in users:
                hash_val = user['password_hash']
                print(f"\n{user['email']}:")
                print(f"  Hash length: {len(hash_val)}")
                print(f"  Starts with $2b$: {hash_val.startswith('$2b$')}")
                print(f"  Hash preview: {hash_val[:20]}...")
                
                # Try to verify with a test password
                try:
                    bcrypt.checkpw(b'test', hash_val.encode('utf-8'))
                    print(f"  Hash format: Valid bcrypt format")
                except Exception as e:
                    print(f"  Hash format: Invalid - {e}")
                    
    except Exception as e:
        print(f"❌ Error checking password hashes: {e}")

def main():
    """Main debug function"""
    print("🚀 Starting login issue diagnosis...\n")
    
    # Test database connection
    if not test_database_connection():
        return
    
    # Examine user data
    users = examine_user_data()
    
    # Check password hash formats
    check_password_hashes()
    
    # Test password verification
    test_password_verification()
    
    # Test authentication function
    test_authentication_function()
    
    print("\n🏁 Diagnosis complete!")

if __name__ == "__main__":
    main()

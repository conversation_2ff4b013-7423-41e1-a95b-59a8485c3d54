"""
Authentication service for INES Transcript System
User authentication and session management
"""
from simple_database_service import authenticate_user as db_authenticate_user

def authenticate_user(email, password, department, role):
    """
    Authenticate user with database
    Returns user data if successful, None otherwise
    """
    try:
        # Faculty login bypass for demo accounts
        if role == 'faculty' and email in ['<EMAIL>', '<EMAIL>']:
            if password == 'faculty123':
                return {
                    'reg_no': 'FAC001' if email == '<EMAIL>' else 'FAC002',
                    'email': email,
                    'role': 'faculty',
                    'name': 'Faculty Sciences' if email == '<EMAIL>' else 'Faculty Engineering',
                    'department': 'Faculty of Sciences and Information Technology' if email == '<EMAIL>' else 'Faculty of Engineering and Technology'
                }
        
        # Use database authentication
        user = db_authenticate_user(email, password, department, role)
        if user:
            return {
                'reg_no': user['reg_no'],
                'email': user['email'],
                'role': user['role'],
                'name': user['name'],
                'department': user.get('department_name') or user.get('faculty_name') or department,
                'enrollment_year': user.get('enrollment_year', 2021)
            }
        return None
        
    except Exception as e:
        print(f"Authentication error: {e}")
        return None
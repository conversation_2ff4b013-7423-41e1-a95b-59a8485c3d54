"""
API routes for INES Transcript System
RESTful API endpoints and AJAX handlers
"""
from flask import Blueprint, jsonify, request, session
from utils.decorators import login_required
from services.student_service import get_student_dashboard_data
from services.chatbot_service import get_chatbot_response

api_bp = Blueprint('api', __name__)

@api_bp.route('/student/dashboard-stats')
@login_required
def student_dashboard_stats():
    """Get student dashboard statistics"""
    if session.get('role') != 'student':
        return jsonify({'error': 'Unauthorized'}), 403
    
    student_id = session['user_id']
    stats = get_student_dashboard_data(student_id, stats_only=True)
    stats['last_updated'] = datetime.now().isoformat()
    
    return jsonify(stats)

@api_bp.route('/chat', methods=['POST'])
def chat():
    """Chatbot API endpoint"""
    try:
        data = request.get_json() if request.is_json else request.form.to_dict()
        user_message = data.get('message', '').strip()
        
        if not user_message:
            return jsonify({
                'response': 'Please type a message! I\'m here to help with the transcript system. 😊'
            })
        
        response = get_chatbot_response(user_message)
        return jsonify({'response': response})
        
    except Exception as e:
        print(f"Chat error: {e}")
        return jsonify({
            'response': 'I\'m having trouble right now. Please try asking about login, requests, or payments.'
        })

@api_bp.route('/health')
def health_check():
    """System health check"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'version': '2.0.0'
    })
#!/usr/bin/env python3
"""
Test script to demonstrate manual payment flow with USSD
"""

import requests
import time
import json

def test_manual_payment_flow():
    """Test the manual payment flow"""
    
    print("🧪 Testing Manual Payment Flow with USSD")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:5000"
    
    # Step 1: Login as student
    print("📝 Step 1: Logging in as student...")
    session = requests.Session()
    
    login_data = {
        'username': 'student1',
        'password': 'password123'
    }
    
    response = session.post(f"{base_url}/login", data=login_data)
    if response.status_code == 200 and 'student/dashboard' in response.url:
        print("   ✅ Login successful")
    else:
        print("   ❌ Login failed")
        return False
    
    # Step 2: Navigate to payment page
    print("\n💳 Step 2: Accessing payment page...")
    response = session.get(f"{base_url}/student/payment")
    if response.status_code == 200:
        print("   ✅ Payment page accessible")
    else:
        print("   ❌ Payment page not accessible")
        return False
    
    # Step 3: Submit phone number for payment
    print("\n📱 Step 3: Submitting phone number for payment...")
    payment_data = {
        'phone_number': '250788123456'
    }
    
    response = session.post(f"{base_url}/student/payment", data=payment_data)
    if response.status_code == 302:  # Redirect to payment status
        print("   ✅ Payment initiated successfully")
        print("   📍 Redirected to payment status page")
    else:
        print(f"   ❌ Payment initiation failed: {response.status_code}")
        return False
    
    # Step 4: Check payment status page
    print("\n⏳ Step 4: Checking payment status page...")
    response = session.get(f"{base_url}/student/payment-status")
    if response.status_code == 200:
        print("   ✅ Payment status page loaded")
        if "*182*7*1#" in response.text:
            print("   ✅ USSD instructions displayed")
        else:
            print("   ⚠️  USSD instructions not found")
    else:
        print("   ❌ Payment status page not accessible")
        return False
    
    # Step 5: Simulate payment status checking
    print("\n🔍 Step 5: Testing payment status API...")
    
    # Get transaction ID from session (simulated)
    transaction_id = "TEST_12345"
    
    for i in range(5):
        print(f"   Check {i+1}: Checking payment status...")
        
        status_data = {
            'transaction_id': transaction_id
        }
        
        response = session.post(
            f"{base_url}/api/check-payment-status",
            json=status_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"   Status: {data.get('status', 'unknown')}")
            print(f"   Message: {data.get('message', 'No message')}")
            
            if data.get('status') == 'completed':
                print("   ✅ Payment completed!")
                break
            elif data.get('status') == 'pending':
                print("   ⏳ Payment pending... (User should complete USSD)")
            else:
                print(f"   ⚠️  Unexpected status: {data.get('status')}")
        else:
            print(f"   ❌ Status check failed: {response.status_code}")
        
        time.sleep(2)  # Wait 2 seconds between checks
    
    print("\n" + "=" * 50)
    print("✅ Manual Payment Flow Test Complete!")
    print("\n📋 Summary:")
    print("1. ✅ Student login working")
    print("2. ✅ Payment page accessible")
    print("3. ✅ Phone number submission working")
    print("4. ✅ Payment status page with USSD instructions")
    print("5. ✅ Payment status API responding")
    print("\n📱 User Experience:")
    print("- User enters phone number")
    print("- System shows USSD code: *182*7*1#")
    print("- User dials USSD and completes payment")
    print("- Browser automatically detects completion")
    print("- Request submitted to dashboard")
    
    return True

def main():
    print("🚀 Manual Payment Flow Tester")
    print("=" * 50)
    print("This script tests the new manual payment flow")
    print("where users complete payments via USSD *182*7*1#")
    print("and the browser detects completion automatically.")
    print()
    
    success = test_manual_payment_flow()
    
    if success:
        print("\n🎉 All tests passed!")
        print("💡 The manual payment flow is ready for use")
    else:
        print("\n❌ Some tests failed")
        print("💡 Check the server logs for details")

if __name__ == "__main__":
    main()

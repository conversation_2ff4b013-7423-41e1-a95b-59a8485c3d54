from flask import session

translations = {
    'en': {
        # Common elements
        'dashboard': 'Dashboard',
        'request_transcript': 'Request Transcript',
        'request_status': 'Request Status',
        'view_downloads': 'View Downloads',
        'logout': 'Logout',
        'reg_no': 'Reg No',
        'submit': 'Submit',
        'cancel': 'Cancel',
        'back': 'Back',
        'next': 'Next',
        'save': 'Save',
        'delete': 'Delete',
        'edit': 'Edit',
        'view': 'View',
        'actions': 'Actions',
        'status': 'Status',
        'date': 'Date',
        'email': 'Email',
        'password': 'Password',
        'name': 'Name',
        'search': 'Search',
        'filter': 'Filter',
        'no_records': 'No Records Found',
        'loading': 'Loading...',
        'error': 'Error',
        'success': 'Success',
        'warning': 'Warning',
        'info': 'Information',
        'welcome': 'Welcome',
        'department': 'Department',
        'pending': 'Pending',
        'approved': 'Approved',
        'rejected': 'Rejected',
        'completed': 'Completed',
        'quick_actions': 'Quick Actions',
        'recent_requests': 'Recent Requests',
        'no_requests': 'No requests found',
        'download': 'Download',
        'download_hint': 'Click the download button to get your transcript',
        'available_transcripts': 'Available Transcripts',
        'no_requests_found': 'No requests found',
        'download_transcript_desc': 'Download your approved transcripts',
        'action': 'Action',
        
        # Request Status Page
        'track_request_status': 'Track the status of your transcript requests',
        'your_requests': 'Your Requests',
        'request_id': 'Request ID',
        'academic_years': 'Academic Years',
        'status_legend': 'Status Legend',
        'pending_finance': 'Pending Finance Approval',
        'approved_finance': 'Approved by Finance',
        'transcript_ready': 'Transcript Ready',
        'request_rejected': 'Request Rejected',
        'not_available': 'Not Available',
        'no_requests_message': 'You haven\'t made any transcript requests yet.',
        'request_new_transcript': 'Request New Transcript',
        
        # Download Transcript Page
        'download_transcript': 'Download Transcript',
        'transcript_details': 'Transcript Details',
        'download_success': 'Transcript downloaded successfully',
        'download_error': 'Error downloading transcript',
        
        # Request Transcript Page
        'request_transcript_desc': 'Request your academic transcript by filling out the form below',
        'purpose': 'Purpose of Request',
        'select_purpose': 'Select Purpose',
        'scholarship': 'Scholarship Application',
        'employment': 'Employment',
        'further_studies': 'Further Studies',
        'other': 'Other',
        'institution': 'Institution/Organization',
        'address': 'Address',
        'phone': 'Phone Number',
        'additional_info': 'Additional Information',
        'submit_request': 'Submit Request',
        'transcript_cost_note': 'Note: Each transcript costs 1,000 RWF. The total will be calculated based on the number of academic years selected.',
        'select_at_least_one_year': 'Please select at least one academic year',
        
        # Request Summary Page
        'request_summary': 'Request Summary',
        'review_request_details': 'Review your request details before proceeding to payment',
        'request_details': 'Request Details',
        'number_of_transcripts': 'Number of Transcripts',
        'price_per_transcript': 'Price per Transcript',
        'total_amount': 'Total Amount',
        'proceed_to_payment': 'Proceed to Payment',
        'back_to_request': 'Back to Request Form',
        'payment_redirect_note': 'Note: You will be redirected to the payment page to complete your request.',
        
        # Email Notifications
        'transcript_request_received': 'Transcript Request Received',
        'dear': 'Dear',
        'request_received_message': 'We have received your transcript request. Here are the details:',
        'request_time': 'Request Time',
        'proceed_to_payment_message': 'Please proceed to the payment page to complete your request.',
        'best_regards': 'Best regards',
        
        # Payment Page
        'payment': 'Payment',
        'choose_payment_method': 'Choose your preferred payment method',
        'payment_details': 'Payment Details',
        'select_payment_method': 'Select Payment Method',
        'momo_payment_desc': 'Pay using your MTN Mobile Money account',
        'flutterwave_payment_desc': 'Pay using credit/debit card or bank transfer',
        'mobile_money_phone': 'Mobile Money Phone Number',
        'phone_placeholder': 'e.g., 078XXXXXXX',
        'flutterwave_redirect_note': 'You will be redirected to Flutterwave secure payment page to complete your payment.',
        'pay_now': 'Pay Now',
        'back_to_summary': 'Back to Summary',
        'secure_payment_note': 'All payments are secure and encrypted.',
        'enter_phone_number': 'Please enter your Mobile Money phone number',

        # Additional translations for dashboard and status explanations
        'status_icons_explanation': 'Status Icons Explanation',
        'pending_explanation': 'Pending - Request is being processed by finance',
        'approved_explanation': 'Approved - Request has been approved and sent to faculty',
        'rejected_explanation': 'Rejected - Request has been rejected by finance',
        'reason_for_rejection': 'Reason for Rejection',
        'download_downloads': 'Downloads',
        'download_desc': 'Download your approved transcripts',
        'date_approved': 'Date Approved',
        'download_count': 'Downloads',
        'times': 'time(s)',
        'last_downloaded': 'Last',
        'not_downloaded': 'Not downloaded yet',
        'downloaded': 'Downloaded',
        'ready_download': 'Ready for Download',
        'download_info': 'Download Information',
        'download_info_1': 'Transcripts can only be downloaded once for security reasons',
        'download_info_2': 'After downloading, the status will change to "Downloaded"',
        'download_info_3': 'If you need another copy, please submit a new request',
        'download_info_4': 'Contact the administration office if you encounter any issues',
        'no_approved_requests': 'No approved requests available for download',
        'no_downloads_desc': 'Once your transcript requests are approved, they will appear here for download.',
    },
    'rw': {
        # Common elements
        'dashboard': 'Urupapuro rw\'ingenzi',
        'request_transcript': 'Gusaba Icyemezo',
        'request_status': 'Imiterere y\'Icyemezo',
        'download_transcript': 'Kuramo Icyemezo',
        'logout': 'Gusohoka',
        'reg_no': 'No y\'Ishyirwa mu Bwoko',
        'submit': 'Ohereza',
        'cancel': 'Reka',
        'back': 'Garuka',
        'next': 'Ibikurikira',
        'save': 'Bika',
        'delete': 'Siba',
        'edit': 'Hindura',
        'view': 'Reba',
        'actions': 'Ibikorwa',
        'status': 'Imiterere',
        'date': 'Itariki',
        'email': 'Imeyili',
        'password': 'Ijambo ry\'ibanga',
        'name': 'Amazina',
        'search': 'Shakisha',
        'filter': 'Tondeka',
        'no_records': 'Nta makuru yavumbuwe',
        'loading': 'Ari gukora...',
        'error': 'Ikibazo',
        'success': 'Byakunze',
        'warning': 'Iburira',
        'info': 'Amakuru',
        
        # Request Transcript Page
        'request_academic_transcript': 'Gusaba Icyemezo cy\'Amashuri',
        'select_academic_years': 'Hitamo Imyaka y\'Amashuri',
        'academic_year': 'Umwaka w\'Amashuri',
        'email_for_updates': 'Imeyili yo Kumenya Imiterere',
        'status_updates_hint': 'Tuzakumenyesha iby\'imiterere y\'icyemezo cyawe',
        'submit_request': 'Ohereza Icyemezo',
        'transcript_cost_note': 'Icyitonderwa: Icyemezo kimwe gishyirwa 1,000 RWF. Amafaranga yose azakadenzwa ku buringanire bw\'imyaka y\'amashuri wahisemo.',
        'select_at_least_one_year': 'Nyamuneka hitamo umwaka w\'amashuri umwe cyangwa myinshi',
        
        # Request Status Page
        'track_request_status': 'Reba imiterere y\'ibyemezo byawe',
        'your_requests': 'Ibyemezo Byawe',
        'request_id': 'No y\'Icyemezo',
        'academic_years': 'Imyaka y\'Amashuri',
        'status_legend': 'Ibisobanuro by\'Imiterere',
        'pending_finance': 'Ari Gutegereza Kugirwa n\'Amafaranga',
        'approved_finance': 'Byemewe n\'Amafaranga',
        'transcript_ready': 'Icyemezo Cyiteguye',
        'request_rejected': 'Icyemezo Cyanzejwe',
        'download': 'Kuramo',
        'not_available': 'Ntibihari',
        'no_requests_found': 'Nta Byemezo Byavumbuwe',
        'no_requests_message': 'Nta cyemezo wahisemo.',
        'request_new_transcript': 'Gusaba Icyemezo Gishya',
        
        # Download Transcript Page
        'available_transcripts': 'Ibyemezo Bihari',
        'download_transcript': 'Kuramo Icyemezo',
        'transcript_details': 'Ibisobanuro by\'Icyemezo',
        'download_hint': 'Kanda kuri "Kuramo" kugirango uramo icyemezo cyawe',
        'download_success': 'Icyemezo cyakuwe neza',
        'download_error': 'Habaye ikibazo mu kuramo icyemezo',
        
        # Payment Page
        'payment': 'Kwishyura',
        'choose_payment_method': 'Hitamo uburyo bwo kwishyura',
        'payment_details': 'Ibisobanuro by\'Amafaranga',
        'select_payment_method': 'Hitamo Uburyo bwo Kwishyura',
        'momo_payment_desc': 'Kwishyura ukoresheje konti yawe ya MTN Mobile Money',
        'flutterwave_payment_desc': 'Kwishyura ukoresheje karite cyangwa kohereza kuri banki',
        'mobile_money_phone': 'Telefoni ya Mobile Money',
        'phone_placeholder': 'urugero, 078XXXXXXX',
        'flutterwave_redirect_note': 'Uzongera kuri Flutterwave kugirango urangize kwishyura kwawe.',
        'pay_now': 'Kwishyura Ubu',
        'back_to_summary': 'Garuka ku Bisobanuro',
        'secure_payment_note': 'Amafaranga yose yishyurwa ari yizewe kandi yafunzwe.',
        'enter_phone_number': 'Nyamuneka andika numero ya telefoni yawe ya Mobile Money',

        # Additional translations for dashboard and status explanations
        'status_icons_explanation': 'Ibisobanuro by\'Ibimenyetso by\'Imiterere',
        'pending_explanation': 'Ari Gutegereza - Icyemezo kiri mu gukora n\'amafaranga',
        'approved_explanation': 'Byemewe - Icyemezo cyemewe kandi cyoherejwe ku ishami',
        'rejected_explanation': 'Byanzejwe - Icyemezo cyanzejwe n\'amafaranga',
        'reason_for_rejection': 'Impamvu yo Kwanza',
        'download_downloads': 'Kuramo',
        'download_desc': 'Kuramo ibyemezo byawe byemewe',
        'date_approved': 'Itariki Yemejwe',
        'download_count': 'Kuramo',
        'times': 'inshuro',
        'last_downloaded': 'Ubwanyuma',
        'not_downloaded': 'Ntibyarakuwe',
        'downloaded': 'Byakuwe',
        'ready_download': 'Biteguye Kuramo',
        'download_info': 'Amakuru yo Kuramo',
        'download_info_1': 'Ibyemezo bishobora gukurwa rimwe gusa kubera umutekano',
        'download_info_2': 'Nyuma yo kuramo, imiterere izahinduka "Byakuwe"',
        'download_info_3': 'Niba ukeneye ikindi kopi, nyamuneka saba icyemezo gishya',
        'download_info_4': 'Vugana n\'ibiro by\'ubuyobozi niba uhura n\'ibibazo',
        'no_approved_requests': 'Nta byemezo byemewe bihari byo kuramo',
        'no_downloads_desc': 'Iyo ibyemezo byawe byemewe, bizagaragara hano byo kuramo.',
    },
    'fr': {
        # Common elements
        'dashboard': 'Tableau de bord',
        'request_transcript': 'Demander un relevé',
        'request_status': 'État de la demande',
        'view_downloads': 'Voir les téléchargements',
        'logout': 'Déconnexion',
        'welcome': 'Bienvenue',
        'department': 'Département',
        'pending': 'En attente',
        'approved': 'Approuvé',
        'rejected': 'Rejeté',
        'quick_actions': 'Actions rapides',
        'recent_requests': 'Demandes récentes',
        'request_id': 'ID de la demande',
        'status': 'Statut',
        'date': 'Date',
        'action': 'Action',
        'view': 'Voir',
        'no_requests': 'Aucune demande trouvée',
        'completed': 'Terminé',
        'reg_no': 'Numéro d\'enregistrement',
        'submit': 'Soumettre',
        'cancel': 'Annuler',
        'back': 'Retour',
        'next': 'Suivant',
        'save': 'Enregistrer',
        'delete': 'Supprimer',
        'edit': 'Modifier',
        'actions': 'Actions',
        'email': 'E-mail',
        'password': 'Mot de passe',
        'name': 'Nom',
        'search': 'Rechercher',
        'filter': 'Filtrer',
        'no_records': 'Aucun enregistrement trouvé',
        'loading': 'Chargement...',
        'error': 'Erreur',
        'success': 'Succès',
        'warning': 'Avertissement',
        'info': 'Information',
        'download': 'Télécharger',
        'download_hint': 'Cliquez sur le bouton de téléchargement pour obtenir votre relevé',
        'available_transcripts': 'Relevés disponibles',
        'no_requests_found': 'Aucune demande trouvée',
        'download_transcript_desc': 'Téléchargez vos relevés approuvés',
        
        # Request Status Page
        'track_request_status': 'Suivre l\'état de vos demandes de relevé',
        'your_requests': 'Vos demandes',
        'academic_years': 'Années académiques',
        'status_legend': 'Légende des statuts',
        'pending_finance': 'En attente d\'approbation financière',
        'approved_finance': 'Approuvé par les finances',
        'transcript_ready': 'Relevé prêt',
        'request_rejected': 'Demande rejetée',
        'not_available': 'Non disponible',
        'no_requests_message': 'Vous n\'avez pas encore fait de demande de relevé.',
        'request_new_transcript': 'Demander un nouveau relevé',
        
        # Request Transcript Page
        'request_transcript_desc': 'Demandez votre relevé académique en remplissant le formulaire ci-dessous',
        'purpose': 'Objet de la demande',
        'select_purpose': 'Sélectionnez l\'objet',
        'scholarship': 'Demande de bourse',
        'employment': 'Emploi',
        'further_studies': 'Études supérieures',
        'other': 'Autre',
        'institution': 'Institution/Organisation',
        'address': 'Adresse',
        'phone': 'Numéro de téléphone',
        'additional_info': 'Informations supplémentaires',
        'submit_request': 'Soumettre la demande',
        'transcript_cost_note': 'Note : Chaque relevé coûte 1 000 RWF. Le total sera calculé en fonction du nombre d\'années académiques sélectionnées.',
        'select_at_least_one_year': 'Veuillez sélectionner au moins une année académique',
        
        # Request Summary Page
        'request_summary': 'Résumé de la demande',
        'review_request_details': 'Vérifiez les détails de votre demande avant de procéder au paiement',
        'request_details': 'Détails de la demande',
        'number_of_transcripts': 'Nombre de relevés',
        'price_per_transcript': 'Prix par relevé',
        'total_amount': 'Montant total',
        'proceed_to_payment': 'Procéder au paiement',
        'back_to_request': 'Retour au formulaire',
        'payment_redirect_note': 'Note : Vous serez redirigé vers la page de paiement pour finaliser votre demande.',
        
        # Email Notifications
        'transcript_request_received': 'Demande de relevé reçue',
        'dear': 'Cher/Chère',
        'request_received_message': 'Nous avons reçu votre demande de relevé. Voici les détails :',
        'request_time': 'Heure de la demande',
        'proceed_to_payment_message': 'Veuillez procéder à la page de paiement pour finaliser votre demande.',
        'best_regards': 'Cordialement',
        
        # Payment Page
        'payment': 'Paiement',
        'choose_payment_method': 'Choisissez votre méthode de paiement préférée',
        'payment_details': 'Détails du paiement',
        'select_payment_method': 'Sélectionnez la méthode de paiement',
        'momo_payment_desc': 'Payer en utilisant votre compte MTN Mobile Money',
        'flutterwave_payment_desc': 'Payer par carte de crédit/débit ou virement bancaire',
        'mobile_money_phone': 'Numéro de téléphone Mobile Money',
        'phone_placeholder': 'ex. : 078XXXXXXX',
        'flutterwave_redirect_note': 'Vous serez redirigé vers la page de paiement sécurisée de Flutterwave pour finaliser votre paiement.',
        'pay_now': 'Payer maintenant',
        'back_to_summary': 'Retour au résumé',
        'secure_payment_note': 'Tous les paiements sont sécurisés et cryptés.',
        'enter_phone_number': 'Veuillez entrer votre numéro de téléphone Mobile Money',

        # Additional translations for dashboard and status explanations
        'status_icons_explanation': 'Explication des icônes de statut',
        'pending_explanation': 'En attente - La demande est en cours de traitement par les finances',
        'approved_explanation': 'Approuvé - La demande a été approuvée et envoyée à la faculté',
        'rejected_explanation': 'Rejeté - La demande a été rejetée par les finances',
        'reason_for_rejection': 'Raison du rejet',
        'download_downloads': 'Téléchargements',
        'download_desc': 'Téléchargez vos relevés approuvés',
        'date_approved': 'Date d\'approbation',
        'download_count': 'Téléchargements',
        'times': 'fois',
        'last_downloaded': 'Dernier téléchargement',
        'not_downloaded': 'Pas encore téléchargé',
        'downloaded': 'Téléchargé',
        'ready_download': 'Prêt pour téléchargement',
        'download_info': 'Informations de téléchargement',
        'download_info_1': 'Les relevés ne peuvent être téléchargés qu\'une seule fois pour des raisons de sécurité',
        'download_info_2': 'Après téléchargement, le statut changera en "Téléchargé"',
        'download_info_3': 'Si vous avez besoin d\'une autre copie, veuillez soumettre une nouvelle demande',
        'download_info_4': 'Contactez le bureau d\'administration si vous rencontrez des problèmes',
        'no_approved_requests': 'Aucune demande approuvée disponible pour téléchargement',
        'no_downloads_desc': 'Une fois vos demandes de relevé approuvées, elles apparaîtront ici pour téléchargement.',
    }
}

def get_translation(key, language='en'):
    """Get translation for a given key in the specified language"""
    if language not in translations:
        language = 'en'  # Default to English if language not found
    return translations[language].get(key, key)

def get_current_language():
    """Get the current language from session or default to English"""
    return session.get('language', 'en')

def set_language(language):
    """Set the current language in session"""
    if language in translations:
        session['language'] = language 
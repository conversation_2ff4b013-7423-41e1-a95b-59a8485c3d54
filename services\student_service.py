"""
Student service layer for INES Transcript System
Business logic for student operations
"""
import os
from datetime import datetime
from werkzeug.utils import secure_filename
from flask import current_app, send_file, flash, redirect, url_for
from simple_database_service import (
    get_requests_by_student_id,
    add_request,
    add_payment
)

def get_student_dashboard_data(student_id, stats_only=False):
    """Get student dashboard data with statistics"""
    requests = get_requests_by_student_id(student_id)
    
    # Calculate statistics
    pending_count = sum(1 for req in requests 
                       if req['status'] in ['pending_finance', 'pending_confirmation'])
    approved_count = sum(1 for req in requests 
                        if req['status'] in ['approved_finance', 'completed'])
    completed_count = sum(1 for req in requests if req['status'] == 'completed')
    rejected_count = sum(1 for req in requests if req['status'] == 'rejected')
    
    stats = {
        'pending_count': pending_count,
        'approved_count': approved_count,
        'completed_count': completed_count,
        'rejected_count': rejected_count,
        'requested_count': len(requests)
    }
    
    if stats_only:
        return stats
    
    # Get recent requests
    recent_requests = sorted(requests, key=lambda x: x.get('date', ''), reverse=True)[:5]
    
    return {
        **stats,
        'requests': recent_requests
    }

def create_transcript_request(student_id, request_data, payment_proof_file):
    """Create new transcript request with payment proof"""
    try:
        # Save payment proof file
        filename = secure_filename(payment_proof_file.filename)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        safe_user_id = str(student_id).replace('/', '_').replace('\\', '_')
        filename = f"payment_proof_{safe_user_id}_{timestamp}_{filename}"
        
        proof_dir = os.path.join(current_app.config['UPLOAD_FOLDER'], 'payment_proofs')
        os.makedirs(proof_dir, exist_ok=True)
        
        file_path = os.path.join(proof_dir, filename)
        payment_proof_file.save(file_path)
        
        # Create request in database
        new_request = add_request(
            student_reg_no=student_id,
            academic_years=request_data['academic_years'],
            payment_method=request_data.get('payment_method', 'mobile_money'),
            total_price=request_data['total_price'],
            purpose='Academic Records',
            institution_name='INES-Ruhengeri',
            payment_proof_filename=filename
        )
        
        if new_request:
            # Add payment record
            add_payment(
                student_reg_no=student_id,
                academic_year=','.join(request_data['academic_years']),
                amount=request_data['total_price'],
                payment_method=request_data.get('payment_method', 'mobile_money'),
                request_id=new_request['id'],
                department=request_data.get('department', 'Unknown')
            )
            return True
        
        return False
        
    except Exception as e:
        print(f"Error creating transcript request: {e}")
        return False

def get_student_requests(student_id, status=None):
    """Get student requests with optional status filter"""
    requests = get_requests_by_student_id(student_id)
    
    if status:
        requests = [req for req in requests if req['status'] == status]
    
    # Add display formatting
    for req in requests:
        req['can_download'] = req['status'] == 'completed'
        req['can_delete'] = req['status'] == 'rejected'
        
        # Status descriptions
        status_descriptions = {
            'pending_finance': 'Waiting for finance verification',
            'pending_confirmation': 'Pending finance confirmation',
            'approved_finance': 'Approved by finance, sent to faculty',
            'completed': 'Transcript ready for download',
            'rejected': 'Request rejected',
            'done': 'Transcript downloaded'
        }
        
        req['status_description'] = status_descriptions.get(
            req['status'], 'Processing your request'
        )
    
    return requests

def download_transcript_file(student_id, request_id):
    """Handle transcript file download"""
    try:
        # Get student's requests
        requests = get_requests_by_student_id(student_id)
        request_data = None
        
        for req in requests:
            if str(req['id']) == str(request_id):
                request_data = req
                break
        
        if not request_data:
            flash('Request not found', 'error')
            return redirect(url_for('student.view_downloads'))
        
        if request_data['status'] != 'completed':
            flash('Transcript not ready for download', 'error')
            return redirect(url_for('student.view_downloads'))
        
        # Check if already downloaded
        if request_data.get('download_count', 0) > 0:
            flash('Transcript already downloaded', 'warning')
            return redirect(url_for('student.view_downloads'))
        
        # Find transcript file
        filename = f"transcript_{request_id}.pdf"
        file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], filename)
        
        if not os.path.exists(file_path):
            flash('Transcript file not found', 'error')
            return redirect(url_for('student.view_downloads'))
        
        # Update download status
        from simple_database_service import get_db_connection
        import pymysql
        
        with get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE transcript_requests 
                SET download_count = 1, downloaded_at = NOW(), status = 'done'
                WHERE id = %s AND download_count = 0
            """, (request_id,))
            
            if cursor.rowcount == 0:
                flash('Transcript already downloaded', 'warning')
                return redirect(url_for('student.view_downloads'))
            
            conn.commit()
        
        # Send file
        return send_file(
            file_path,
            as_attachment=True,
            download_name=f"INES_Transcript_{request_data['student_name'].replace(' ', '_')}_{request_id}.pdf",
            mimetype='application/pdf'
        )
        
    except Exception as e:
        print(f"Error downloading transcript: {e}")
        flash('Error downloading transcript', 'error')
        return redirect(url_for('student.view_downloads'))
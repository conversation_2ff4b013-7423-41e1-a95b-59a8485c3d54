"""
Diagnose and fix the remaining issues:
1. Payment proof not being sent properly for "Submit Without Validation"
2. Finance approve button delays and functionality
3. Rejection date showing N/A instead of real date
4. Approve button not working for all departments
5. Faculty routing by department
"""
import sys

def test_payment_proof_issue():
    """Test payment proof upload and storage"""
    try:
        print("🔍 Testing payment proof upload...")
        
        from simple_database_service import get_all_requests
        
        # Get recent requests and check payment proof filenames
        all_requests = get_all_requests()
        
        print(f"📊 Found {len(all_requests)} total requests")
        
        for req in all_requests[-3:]:  # Check last 3 requests
            proof_filename = req.get('payment_proof_filename')
            request_id = req.get('id')
            
            print(f"\n📋 Request {request_id}:")
            print(f"   - Payment Proof: {proof_filename}")
            print(f"   - Payment Method: {req.get('payment_method', 'N/A')}")
            print(f"   - Payment Status: {req.get('payment_status', 'N/A')}")
            
            if proof_filename and proof_filename != 'None':
                print("✅ Payment proof filename stored correctly")
            else:
                print("❌ Payment proof filename missing or None")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing payment proof: {e}")
        return False

def test_finance_approval_performance():
    """Test finance approval function performance"""
    try:
        print("\n🔍 Testing finance approval performance...")
        
        import time
        from simple_database_service import add_request, approve_transcript_request
        
        # Create a test request
        print("📝 Creating test request...")
        start_time = time.time()
        
        new_request = add_request(
            student_reg_no='STU001',
            academic_years=['2023-2024'],
            payment_method='mobile_money',
            total_price=1000,
            purpose='Academic Records',
            institution_name='INES-Ruhengeri',
            payment_proof_filename='test_payment.jpg'
        )
        
        create_time = time.time() - start_time
        print(f"✅ Request created in {create_time:.2f} seconds")
        
        if new_request:
            request_id = new_request['id']
            
            # Test approval performance
            print(f"🔄 Testing approval of request {request_id}...")
            start_time = time.time()
            
            success = approve_transcript_request(request_id, 'FIN001')
            
            approval_time = time.time() - start_time
            print(f"📊 Approval took {approval_time:.2f} seconds")
            
            if approval_time > 5:
                print("⚠️  Approval is slow (>5 seconds)")
                print("   Possible causes:")
                print("   - Email notification delays")
                print("   - Database query performance")
                print("   - Network connectivity issues")
            else:
                print("✅ Approval performance is acceptable")
            
            if success:
                print("✅ Approval function succeeded")
                return True
            else:
                print("❌ Approval function failed")
                return False
        else:
            print("❌ Failed to create test request")
            return False
            
    except Exception as e:
        print(f"❌ Error testing approval performance: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_rejection_date_display():
    """Test rejection date display in finance history"""
    try:
        print("\n🔍 Testing rejection date display...")
        
        from simple_database_service import get_finance_dashboard_data, add_request, reject_transcript_request
        
        # Create and reject a test request
        print("📝 Creating test request for rejection...")
        new_request = add_request(
            student_reg_no='STU001',
            academic_years=['2023-2024'],
            payment_method='mobile_money',
            total_price=1000,
            purpose='Academic Records',
            institution_name='INES-Ruhengeri',
            payment_proof_filename='test_payment.jpg'
        )
        
        if new_request:
            request_id = new_request['id']
            print(f"✅ Test request created: {request_id}")
            
            # Reject the request
            print(f"🔄 Rejecting request {request_id}...")
            success = reject_transcript_request(request_id, 'FIN001', 'Test rejection for date check')
            
            if success:
                print("✅ Rejection function succeeded")
                
                # Check finance dashboard
                dashboard_data = get_finance_dashboard_data()
                rejected_requests = dashboard_data['rejected_requests']
                
                # Find our rejected request
                rejected_request = None
                for req in rejected_requests:
                    if req['id'] == str(request_id):
                        rejected_request = req
                        break
                
                if rejected_request:
                    rejected_date = rejected_request.get('rejected_date', 'N/A')
                    print(f"📊 Rejected request shows:")
                    print(f"   - Rejected Date: {rejected_date}")
                    
                    if rejected_date and rejected_date != 'N/A':
                        print("✅ Rejection date is properly displayed")
                        return True
                    else:
                        print("❌ Rejection date shows N/A")
                        print("   Checking database fields...")
                        
                        # Check what's in the database
                        from simple_database_service import get_db_connection
                        import pymysql
                        
                        with get_db_connection() as conn:
                            cursor = conn.cursor(pymysql.cursors.DictCursor)
                            cursor.execute("SELECT * FROM transcript_requests WHERE id = %s", (request_id,))
                            db_req = cursor.fetchone()
                            
                            if db_req:
                                print(f"   - auto_processed_at: {db_req.get('auto_processed_at')}")
                                print(f"   - auto_decision: {db_req.get('auto_decision')}")
                                print(f"   - auto_decision_reason: {db_req.get('auto_decision_reason')}")
                        
                        return False
                else:
                    print("❌ Rejected request not found in finance dashboard")
                    return False
            else:
                print("❌ Rejection function failed")
                return False
        else:
            print("❌ Failed to create test request")
            return False
            
    except Exception as e:
        print(f"❌ Error testing rejection date: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_department_faculty_routing():
    """Test faculty routing by department"""
    try:
        print("\n🔍 Testing department to faculty routing...")
        
        from app import get_pending_requests_for_faculty
        from simple_database_service import add_request, approve_transcript_request
        
        # Test different departments
        test_departments = [
            ('Computer Science', 'Faculty of Sciences and Information Technology'),
            ('Civil Engineering', 'Faculty of Engineering and Technology'),
            ('Law', 'Faculty of Law')
        ]
        
        for dept, expected_faculty in test_departments:
            print(f"\n📋 Testing {dept} → {expected_faculty}")
            
            # Create and approve a request for this department
            # Note: This would require creating a user with the specific department
            # For now, let's test the mapping function
            
            faculty_dept_map = {
                'Faculty of Sciences and Information Technology': ['Computer Science', 'Statistics Applied to Economy'],
                'Faculty of Engineering and Technology': ['Civil Engineering', 'Architecture', 'Biotechnologies', 'Water Engineering', 'Surveying', 'Land Survey', 'Masonry', 'Welding', 'Domestic Plumbing'],
                'Faculty of Economics Social Sciences and Management': ['Cooperatives Management', 'Entrepreneurship and SME\'s Management'],
                'Faculty of Education': ['French and English'],
                'Faculty of Law': ['Law']
            }
            
            def belongs_to_faculty(request_dept, faculty_name):
                return request_dept in faculty_dept_map.get(faculty_name, [])
            
            result = belongs_to_faculty(dept, expected_faculty)
            
            if result:
                print(f"✅ {dept} correctly maps to {expected_faculty}")
            else:
                print(f"❌ {dept} does not map to {expected_faculty}")
                print(f"   Available mappings: {faculty_dept_map}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing faculty routing: {e}")
        return False

def main():
    """Diagnose and fix all issues"""
    print("🔍 INES Transcript System - Issue Diagnosis & Fixes")
    print("=" * 60)
    
    tests = [
        ("Payment Proof Upload", test_payment_proof_issue),
        ("Finance Approval Performance", test_finance_approval_performance),
        ("Rejection Date Display", test_rejection_date_display),
        ("Department Faculty Routing", test_department_faculty_routing)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*55}")
        print(f"Testing: {test_name}")
        print('='*55)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name} - PASSED")
        else:
            print(f"❌ {test_name} - FAILED")
    
    print(f"\n📊 Diagnosis Results: {passed}/{total} tests passed")
    
    if passed < total:
        print("\n🔧 ISSUES IDENTIFIED:")
        print("1. Check payment proof filename storage")
        print("2. Optimize approval function performance")
        print("3. Fix rejection date extraction from database")
        print("4. Verify faculty department mapping")
        print("5. Check email notification delays")
    else:
        print("\n🎉 All systems working correctly!")
    
    return passed >= 3

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>INES-Ruhengeri Transcript System - Login</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .dropdown-container {
            display: none;
        }
        .dropdown-container.active {
            display: block;
        }
        .help-link {
            color: #007bff;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }
        .help-link:hover {
            color: #0056b3;
            text-decoration: underline;
        }
        .login-info {
            text-align: center;
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .role-selection {
            margin: 20px 0;
            text-align: center;
        }
        .role-selection p {
            margin-bottom: 15px;
            color: #666;
            font-weight: 500;
        }
        .role-options {
            display: flex;
            justify-content: center;
            gap: 20px;
        }
        .role-option {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 15px 25px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            background-color: #fff;
        }
        .role-option:hover {
            border-color: #007bff;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .role-option.selected {
            border-color: #007bff;
            background-color: #f0f7ff;
        }
        .role-option i {
            font-size: 24px;
            margin-bottom: 8px;
            color: #007bff;
        }
        .role-option span {
            font-weight: 500;
            color: #333;
        }

        /* Chatbot Styles */
        .chat-button {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-color: #075e54;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            z-index: 1000;
            transition: transform 0.3s ease;
        }

        .chat-button:hover {
            transform: scale(1.1);
        }

        .chat-window {
            position: fixed;
            top: 80px;
            right: 20px;
            width: 350px;
            height: 500px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            display: none;
            flex-direction: column;
            z-index: 1000;
        }

        .chat-window.active {
            display: flex;
        }

        .chat-header {
            padding: 15px;
            background-color: #075e54;
            color: white;
            border-radius: 10px 10px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .chat-header h3 {
            margin: 0;
            font-size: 1.1rem;
        }

        .close-chat {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            font-size: 1.2rem;
        }

        .chat-messages {
            flex: 1;
            padding: 15px;
            overflow-y: auto;
        }

        .message {
            margin-bottom: 10px;
            padding: 8px 12px;
            border-radius: 15px;
            max-width: 80%;
            word-wrap: break-word;
        }

        .message.user {
            background-color: #dcf8c6;
            margin-left: auto;
            border-bottom-right-radius: 5px;
        }

        .message.bot {
            background-color: #e8e8e8;
            margin-right: auto;
            border-bottom-left-radius: 5px;
        }

        .chat-input {
            padding: 15px;
            border-top: 1px solid #eee;
            display: flex;
            gap: 10px;
        }

        .chat-input input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 20px;
            outline: none;
        }

        .chat-input button {
            padding: 8px 15px;
            background-color: #075e54;
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
        }

        .chat-input button:hover {
            background-color: #128c7e;
        }

        .quick-actions {
            padding: 10px 15px;
            border-top: 1px solid #eee;
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
        }

        .quick-action-btn {
            padding: 5px 10px;
            background-color: #f0f0f0;
            border: 1px solid #ddd;
            border-radius: 15px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .quick-action-btn:hover {
            background-color: #e0e0e0;
        }

        .typing-indicator {
            display: none;
            padding: 8px 12px;
            background-color: #f0f0f0;
            border-radius: 15px;
            margin-bottom: 10px;
            max-width: 80%;
        }

        .typing-indicator.show {
            display: block;
        }

        .typing-dots {
            display: inline-block;
        }

        .typing-dots span {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #999;
            margin: 0 1px;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dots span:nth-child(1) { animation-delay: -0.32s; }
        .typing-dots span:nth-child(2) { animation-delay: -0.16s; }

        @keyframes typing {
            0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }
    </style>
</head>
<body>
    <!-- Chat Button -->
    <div class="chat-button" onclick="toggleChat()">
        <i class="fas fa-comments"></i>
    </div>

    <!-- Chat Window -->
    <div class="chat-window" id="chatWindow">
        <div class="chat-header">
            <h3>Transcript System Assistant</h3>
            <button class="close-chat" onclick="toggleChat()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="chat-messages" id="chatMessages">
            <div class="message bot">
                What can I help with? 😊
            </div>
            <div class="typing-indicator" id="typingIndicator">
                <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
                Assistant is typing...
            </div>
        </div>
        <div class="quick-actions">
            <button class="quick-action-btn" onclick="sendQuickMessage('How do I login to the system?')">🔐 Login Help</button>
            <button class="quick-action-btn" onclick="sendQuickMessage('How do I request a transcript?')">📄 Request Guide</button>
            <button class="quick-action-btn" onclick="sendQuickMessage('What are the payment methods?')">💳 Payment Info</button>
            <button class="quick-action-btn" onclick="sendQuickMessage('What are the different user roles?')">👥 User Roles</button>
            <button class="quick-action-btn" onclick="sendQuickMessage('How can I get help or contact support?')">📞 Contact</button>
        </div>
        <div class="chat-input">
            <input type="text" id="userInput" placeholder="Type your message...">
            <button onclick="sendMessage()">
                <i class="fas fa-paper-plane"></i>
            </button>
        </div>
    </div>

    <div class="login-container">
        <div class="login-header">
            <img src="{{ url_for('static', filename='images/ines-logo.png') }}" alt="INES-Ruhengeri Logo" class="logo">
            <h1>Academic Transcript System</h1>
            <p>INES-Ruhengeri</p>
        </div>
        
        <div class="login-form-container">
            <div class="form-header">
                <h2>Login</h2>
                <p>Please enter your credentials to access the system</p>
            </div>
            
            {% if error %}
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle"></i> {{ error }}
            </div>
            {% endif %}
            
            <div class="role-selection">
                <p>Select your role:</p>
                <div class="role-options">
                    <div class="role-option" onclick="selectRole('student')">
                        <i class="fas fa-user-graduate"></i>
                        <span>Student</span>
                    </div>
                    <div class="role-option" onclick="selectRole('finance')">
                        <i class="fas fa-money-bill-wave"></i>
                        <span>Finance</span>
                    </div>
                    <div class="role-option" onclick="selectRole('faculty')">
                        <i class="fas fa-chalkboard-teacher"></i>
                        <span>Faculty</span>
                    </div>
                </div>
            </div>
            
            <form action="{{ url_for('login') }}" method="POST" class="login-form">
                <!-- Department Dropdown (for students) -->
                <div id="departmentContainer" class="dropdown-container">
                    <div class="form-group">
                        <label for="department">Department</label>
                        <div class="input-group">
                            <span class="input-icon"><i class="fas fa-building"></i></span>
                            <select id="department" name="department">
                                <option value="">Select Department</option>
                                <option value="Computer Science">Computer Science</option>
                                <option value="Statistics Applied to Economy">Statistics Applied to Economy</option>
                                <option value="Cooperatives Management">Cooperatives Management</option>
                                <option value="Entrepreneurship and SME's Management">Entrepreneurship and SME's Management</option>
                                <option value="French and English">French and English</option>
                                <option value="Civil Engineering">Civil Engineering</option>
                                <option value="Biotechnologies">Biotechnologies</option>
                                <option value="Land Survey">Land Survey</option>
                                <option value="Architecture">Architecture</option>
                                <option value="Water Engineering">Water Engineering</option>
                                <option value="Masonry">Masonry</option>
                                <option value="Welding">Welding</option>
                                <option value="Domestic Plumbing">Domestic Plumbing</option>
                                <option value="Surveying">Surveying</option>
                                <option value="Master of Science in Food Science and Technology">Master of Science in Food Science and Technology</option>
                                <option value="Master of Science in Geo-Informatics">Master of Science in Geo-Informatics</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Faculty Dropdown (for faculty members) -->
                <div id="facultyContainer" class="dropdown-container">
                    <div class="form-group">
                        <label for="faculty">Faculty</label>
                        <div class="input-group">
                            <span class="input-icon"><i class="fas fa-university"></i></span>
                            <select id="faculty" name="faculty">
                                <option value="">Select Faculty</option>
                                <option value="Faculty of Sciences and Information Technology">Faculty of Sciences and Information Technology</option>
                                <option value="Faculty of Economics Social Sciences and Management">Faculty of Economics Social Sciences and Management</option>
                                <option value="Faculty of Education">Faculty of Education</option>
                                <option value="Faculty of Engineering and Technology">Faculty of Engineering and Technology</option>
                                <option value="Faculty of Law and Public Administration">Faculty of Law and Public Administration</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="email">Email</label>
                    <div class="input-group">
                        <span class="input-icon"><i class="fas fa-envelope"></i></span>
                        <input type="email" id="email" name="email" placeholder="Enter your INES email" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="password">Password</label>
                    <div class="input-group">
                        <span class="input-icon"><i class="fas fa-lock"></i></span>
                        <input type="password" id="password" name="password" placeholder="Enter your password" required>
                        <span class="toggle-password" onclick="togglePassword()">
                            <i class="fas fa-eye"></i>
                        </span>
                    </div>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn btn-primary btn-block">
                        <i class="fas fa-sign-in-alt"></i> Login
                    </button>
                </div>
            </form>
            
            <div class="login-info">
                <p>Need help? Visit <a href="https://www.ines.ac.rw" target="_blank" class="help-link">INES Ruhengeri Official Website</a> for support.</p>
            </div>
        </div>
    </div>
    
    <footer class="main-footer">
        <p>&copy; 2025 INES-Ruhengeri. All rights reserved.</p>
    </footer>
    
    <script>
        let currentRole = '';

        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const icon = document.querySelector('.toggle-password i');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }
        
        function selectRole(role) {
            currentRole = role;
            const departmentContainer = document.getElementById('departmentContainer');
            const facultyContainer = document.getElementById('facultyContainer');
            const departmentSelect = document.getElementById('department');
            const facultySelect = document.getElementById('faculty');
            
            // Hide all dropdowns first
            departmentContainer.classList.remove('active');
            facultyContainer.classList.remove('active');
            
            // Show relevant dropdown based on role
            if (role === 'student') {
                departmentContainer.classList.add('active');
                departmentSelect.required = true;
                facultySelect.required = false;
            } else if (role === 'faculty') {
                facultyContainer.classList.add('active');
                facultySelect.required = true;
                departmentSelect.required = false;
            } else {
                // For finance role, no dropdown is required
                departmentSelect.required = false;
                facultySelect.required = false;
            }
            
            // Update form action to include role
            const form = document.querySelector('.login-form');
            form.action = "{{ url_for('login') }}?role=" + role;
            
            // Highlight selected role
            const roleOptions = document.querySelectorAll('.role-option');
            roleOptions.forEach(option => {
                option.classList.remove('selected');
                if (option.querySelector('span').textContent.toLowerCase() === role) {
                    option.classList.add('selected');
                }
            });
        }

        // Chatbot Functions
        function toggleChat() {
            const chatWindow = document.getElementById('chatWindow');
            chatWindow.classList.toggle('active');
        }

        function sendMessage() {
            const input = document.getElementById('userInput');
            const message = input.value.trim();

            if (message) {
                // Add user message to chat
                addMessage(message, 'user');
                input.value = '';

                // Show typing indicator
                showTypingIndicator();

                // Send message to server
                fetch('/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ message: message })
                })
                .then(response => response.json())
                .then(data => {
                    hideTypingIndicator();
                    addMessage(data.response, 'bot');
                })
                .catch(error => {
                    console.error('Error:', error);
                    hideTypingIndicator();
                    addMessage('Sorry, something went wrong. Please try again! 🔧', 'bot');
                });
            }
        }

        function sendQuickMessage(message) {
            const input = document.getElementById('userInput');
            input.value = message;
            sendMessage();
        }

        function showTypingIndicator() {
            const indicator = document.getElementById('typingIndicator');
            indicator.classList.add('show');
            const messagesDiv = document.getElementById('chatMessages');
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        function hideTypingIndicator() {
            const indicator = document.getElementById('typingIndicator');
            indicator.classList.remove('show');
        }

        function addMessage(message, sender) {
            const messagesDiv = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;

            // Convert line breaks to HTML and preserve formatting
            const formattedMessage = message
                .replace(/\n/g, '<br>')
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                .replace(/\*(.*?)\*/g, '<em>$1</em>');

            messageDiv.innerHTML = formattedMessage;
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        // Allow sending message with Enter key
        document.addEventListener('DOMContentLoaded', function() {
            const userInput = document.getElementById('userInput');
            if (userInput) {
                userInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        sendMessage();
                    }
                });
            }
        });
    </script>
</body>
</html> 
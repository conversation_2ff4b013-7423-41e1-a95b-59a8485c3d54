"""
Faculty service layer for INES Transcript System
Business logic for faculty operations
"""
import os
from datetime import datetime
from flask import current_app
from simple_database_service import (
    get_pending_requests_for_faculty,
    get_completed_requests_for_faculty,
    get_request_by_id,
    add_transcript
)

# Faculty department mapping
FACULTY_DEPT_MAP = {
    'Faculty of Sciences and Information Technology': ['Computer Science', 'Statistics Applied to Economy'],
    'Faculty of Engineering and Technology': ['Civil Engineering', 'Architecture', 'Biotechnologies', 'Water Engineering', 'Surveying', 'Land Survey', 'Masonry', 'Welding', 'Domestic Plumbing'],
    'Faculty of Economics Social Sciences and Management': ['Cooperatives Management', 'Entrepreneurship and SME\'s Management'],
    'Faculty of Education': ['French and English'],
    'Faculty of Law': ['Law']
}

def belongs_to_faculty(request_dept, faculty_name):
    """Check if department belongs to faculty"""
    return request_dept in FACULTY_DEPT_MAP.get(faculty_name, [])

def get_faculty_dashboard_data(faculty_department):
    """Get faculty dashboard data"""
    all_pending = get_pending_requests_for_faculty()
    all_completed = get_completed_requests_for_faculty()
    
    # Filter by faculty
    pending_requests = [req for req in all_pending 
                       if belongs_to_faculty(req.get('department', ''), faculty_department)]
    completed_requests = [req for req in all_completed 
                         if belongs_to_faculty(req.get('department', ''), faculty_department)]
    
    return {
        'pending_count': len(pending_requests),
        'completed_count': len(completed_requests),
        'requests': pending_requests,
        'completed_requests': completed_requests
    }

def get_faculty_requests(faculty_department, status=None):
    """Get faculty requests with optional status filter"""
    if status == 'approved_finance':
        all_requests = get_pending_requests_for_faculty()
    elif status == 'completed':
        all_requests = get_completed_requests_for_faculty()
    else:
        # Get all requests
        all_requests = get_pending_requests_for_faculty() + get_completed_requests_for_faculty()
    
    # Filter by faculty
    return [req for req in all_requests 
            if belongs_to_faculty(req.get('department', ''), faculty_department)]

def upload_transcript_files(request_id, files, faculty_department):
    """Upload transcript files for a request"""
    try:
        # Get request data
        request_data = get_request_by_id(request_id)
        if not request_data:
            print(f"Request {request_id} not found")
            return False
        
        # Verify request belongs to faculty
        if not belongs_to_faculty(request_data.get('department', ''), faculty_department):
            print(f"Request {request_id} does not belong to faculty {faculty_department}")
            return False
        
        # Save files
        saved_filenames = []
        for i, file in enumerate(files):
            if len(files) == 1:
                filename = f"transcript_{request_id}.pdf"
            else:
                filename = f"transcript_{request_id}_{i+1}.pdf"
            
            file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], filename)
            file.save(file_path)
            saved_filenames.append(filename)
        
        # Update database
        main_filename = saved_filenames[0] if saved_filenames else f"transcript_{request_id}.pdf"
        
        success = add_transcript(
            request_id,
            request_data.get('student_id'),
            request_data.get('student_name'),
            request_data.get('academic_years', []),
            main_filename
        )
        
        if success:
            # Send notification to student
            from services.notification_service import notify_student_transcript_ready
            notify_student_transcript_ready(request_data)
            return True
        
        return False
        
    except Exception as e:
        print(f"Error uploading transcript files: {e}")
        return False
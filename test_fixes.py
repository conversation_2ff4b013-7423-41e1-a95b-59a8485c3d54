#!/usr/bin/env python3
"""
Test script to verify both fixes:
1. Single academic year selection (radio buttons)
2. Fixed payment flow with proper error handling
"""

import requests
import json

def test_single_academic_year_selection():
    """Test that the form now uses radio buttons for single selection"""
    print("🧪 Testing Single Academic Year Selection")
    print("=" * 50)
    
    session = requests.Session()
    
    try:
        # Login as student
        print("1. Logging in as student...")
        login_data = {
            'username': 'student1',
            'password': 'password123'
        }
        response = session.post("http://localhost:5000/login", data=login_data)
        if response.status_code == 200 and 'student' in response.text.lower():
            print("✅ Student login successful")
        else:
            print(f"❌ Login failed: {response.status_code}")
            return False
            
        # Check transcript request page
        print("2. Checking transcript request page...")
        response = session.get("http://localhost:5000/student/request-transcript")
        if response.status_code == 200:
            # Check if radio buttons are used instead of checkboxes
            if 'type="radio"' in response.text and 'name="academic_years"' in response.text:
                print("✅ Radio buttons found for academic year selection")
                if 'Select ONE completed academic year' in response.text:
                    print("✅ Instructions updated to indicate single selection")
                else:
                    print("⚠️  Instructions not updated")
                    
                # Count radio buttons
                radio_count = response.text.count('type="radio"')
                print(f"📊 Found {radio_count} radio button(s) for academic years")
                
                return True
            else:
                print("❌ Radio buttons not found - still using checkboxes")
                return False
        else:
            print(f"❌ Failed to access transcript request page: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def test_payment_flow():
    """Test the payment flow with proper error handling"""
    print("\n🧪 Testing Payment Flow")
    print("=" * 50)
    
    session = requests.Session()
    
    try:
        # Login as student
        print("1. Logging in as student...")
        login_data = {
            'username': 'student1',
            'password': 'password123'
        }
        response = session.post("http://localhost:5000/login", data=login_data)
        if response.status_code != 200:
            print(f"❌ Login failed: {response.status_code}")
            return False
        print("✅ Student login successful")
        
        # Submit transcript request
        print("2. Submitting transcript request...")
        request_data = {
            'academic_years': '2023-2024',  # Single selection now
            'email': '<EMAIL>',
            'transcript_type': 'official',
            'delivery_method': 'pickup',
            'purpose': 'Job Application'
        }
        response = session.post("http://localhost:5000/student/request-transcript", data=request_data)
        
        if response.status_code == 200 and ('summary' in response.url or 'payment' in response.url):
            print("✅ Transcript request submitted successfully")
        else:
            print(f"⚠️  Request submission response: {response.status_code}, URL: {response.url}")
        
        # Try payment
        print("3. Testing payment flow...")
        payment_data = {
            'payment_method': 'mobile_money',
            'phone': '250788123456'
        }
        response = session.post("http://localhost:5000/student/payment", data=payment_data)
        
        print(f"Payment response status: {response.status_code}")
        print(f"Payment response URL: {response.url}")
        
        if 'payment-status' in response.url:
            print("✅ Payment flow working - redirected to status page")
            
            # Check payment status page
            response = session.get(response.url)
            if response.status_code == 200:
                if 'USSD' in response.text and '*182*7*1#' in response.text:
                    print("✅ USSD instructions found on payment status page")
                    return True
                else:
                    print("⚠️  USSD instructions not found")
                    return False
            else:
                print(f"❌ Failed to access payment status page: {response.status_code}")
                return False
        else:
            print("❌ Payment flow failed - not redirected to status page")
            return False
            
    except Exception as e:
        print(f"❌ Payment test failed: {e}")
        return False

def test_momo_api_directly():
    """Test MoMo API directly"""
    print("\n🧪 Testing MoMo API Directly")
    print("=" * 50)
    
    try:
        from services.momo_api import momo_api
        
        print(f"Initial test mode: {momo_api.test_mode}")
        
        # Test payment request
        result = momo_api.request_to_pay('250788123456', 1000)
        
        print(f"Payment result: {result}")
        
        if result and result.get('success'):
            print("✅ MoMo API working correctly")
            if result.get('test_mode'):
                print("✅ Test mode enabled due to API outage")
            if 'USSD' in result.get('message', ''):
                print("✅ USSD instructions included in response")
            return True
        else:
            print("❌ MoMo API failed")
            return False
            
    except Exception as e:
        print(f"❌ MoMo API test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 RUNNING COMPREHENSIVE TESTS FOR FIXES")
    print("=" * 60)
    
    # Test 1: Single academic year selection
    test1_result = test_single_academic_year_selection()
    
    # Test 2: Payment flow
    test2_result = test_payment_flow()
    
    # Test 3: MoMo API directly
    test3_result = test_momo_api_directly()
    
    print("\n" + "=" * 60)
    print("🎯 TEST RESULTS SUMMARY")
    print("=" * 60)
    print(f"✅ Single Academic Year Selection: {'PASS' if test1_result else 'FAIL'}")
    print(f"✅ Payment Flow: {'PASS' if test2_result else 'FAIL'}")
    print(f"✅ MoMo API: {'PASS' if test3_result else 'FAIL'}")
    
    if all([test1_result, test2_result, test3_result]):
        print("\n🎉 ALL TESTS PASSED! Both fixes are working correctly.")
    else:
        print("\n⚠️  Some tests failed. Please check the output above.")

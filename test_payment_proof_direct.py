"""
Test payment proof storage directly from database
"""
import sys

def test_payment_proof_direct():
    """Test payment proof storage by querying database directly"""
    try:
        print("🔍 Testing payment proof storage directly...")
        
        from simple_database_service import add_request, get_db_connection
        import pymysql
        
        # Create a request with payment proof filename
        print("📝 Creating request with payment proof...")
        new_request = add_request(
            student_reg_no='STU001',
            academic_years=['2023-2024'],
            payment_method='mobile_money',
            total_price=1000,
            purpose='Academic Records',
            institution_name='INES-Ruhengeri',
            payment_proof_filename='test_payment_proof_direct_20250626.jpg'
        )
        
        if new_request:
            request_id = new_request['id']
            print(f"✅ Request created: {request_id}")
            
            # Query database directly
            with get_db_connection() as conn:
                cursor = conn.cursor(pymysql.cursors.DictCursor)
                cursor.execute("SELECT * FROM transcript_requests WHERE id = %s", (request_id,))
                db_request = cursor.fetchone()
                
                if db_request:
                    proof_filename = db_request.get('payment_proof_filename')
                    print(f"📊 Database shows:")
                    print(f"   - ID: {db_request['id']}")
                    print(f"   - Payment Proof: {proof_filename}")
                    print(f"   - Payment Method: {db_request.get('payment_method')}")
                    print(f"   - Payment Status: {db_request.get('payment_status')}")
                    
                    if proof_filename and 'test_payment_proof_direct' in proof_filename:
                        print("✅ Payment proof filename stored correctly in database")
                        return True
                    else:
                        print("❌ Payment proof filename not stored properly")
                        print(f"   Expected: containing 'test_payment_proof_direct'")
                        print(f"   Actual: {proof_filename}")
                        return False
                else:
                    print("❌ Request not found in database")
                    return False
        else:
            print("❌ Failed to create request")
            return False
            
    except Exception as e:
        print(f"❌ Error testing payment proof: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_existing_requests_proof():
    """Check existing requests for payment proof"""
    try:
        print("\n🔍 Checking existing requests for payment proof...")
        
        from simple_database_service import get_db_connection
        import pymysql
        
        with get_db_connection() as conn:
            cursor = conn.cursor(pymysql.cursors.DictCursor)
            cursor.execute("""
                SELECT id, payment_proof_filename, payment_method, payment_status, created_at 
                FROM transcript_requests 
                ORDER BY created_at DESC 
                LIMIT 5
            """)
            requests = cursor.fetchall()
            
            print(f"📊 Last 5 requests:")
            for req in requests:
                print(f"\n📋 Request {req['id']}:")
                print(f"   - Proof: {req.get('payment_proof_filename', 'NULL')}")
                print(f"   - Method: {req.get('payment_method', 'NULL')}")
                print(f"   - Status: {req.get('payment_status', 'NULL')}")
                print(f"   - Created: {req.get('created_at', 'NULL')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking existing requests: {e}")
        return False

def main():
    """Test payment proof storage"""
    print("🔍 INES Transcript System - Payment Proof Storage Test")
    print("=" * 60)
    
    tests = [
        ("Payment Proof Direct Test", test_payment_proof_direct),
        ("Existing Requests Check", test_existing_requests_proof)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Testing: {test_name}")
        print('='*50)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name} - PASSED")
        else:
            print(f"❌ {test_name} - FAILED")
    
    print(f"\n📊 Payment Proof Test Results: {passed}/{total} tests passed")
    
    return passed >= 1

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

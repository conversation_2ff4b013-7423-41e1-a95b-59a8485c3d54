<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Fees | INES-Ruhengeri Transcript System</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .sidebar-header {
            text-align: center;
            padding: 20px;
            border-bottom: 1px solid #e0e0e0;
        }
        .profile-image {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #007bff;
            margin-bottom: 15px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .sidebar-header h2 {
            color: #fff;
            font-size: 1.2rem;
            margin: 10px 0 5px;
            font-weight: 600;
        }
        .sidebar-header p {
            color: #fff;
            font-size: 0.9rem;
            margin: 0;
        }
        .dashboard-header h1 {
            color: #333;
            font-size: 1.8rem;
            margin-bottom: 10px;
        }
        .dashboard-header p {
            color: #666;
            font-size: 1rem;
        }
        .faculty-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
            max-width: 800px;
        }
        .faculty-box {
            border: 2px solid #666;
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            min-height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .faculty-box:hover {
            background: #e9ecef;
            border-color: #333;
        }
        .faculty-box a {
            text-decoration: none;
            color: #333;
            font-size: 1rem;
            font-weight: 500;
            display: block;
            width: 100%;
        }
        .faculty-box a:hover {
            color: #333;
            text-decoration: none;
        }
        .content-area {
            background: #f0f0f0;
            min-height: 500px;
            padding: 40px;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <img src="{{ url_for('static', filename='images/user.jpeg') }}" alt="Finance Profile" class="profile-image">
                <h2>{{ session.name }}</h2>
                <p>Finance ID: {{ session.user_id }}</p>
            </div>
            
            <nav class="sidebar-menu">
                <ul>
                    <li>
                        <a href="{{ url_for('finance_dashboard') }}">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('view_status') }}">
                            <i class="fas fa-eye"></i> View Status
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('view_history') }}">
                            <i class="fas fa-history"></i> View History
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('manage_fees') }}" class="active">
                            <i class="fas fa-money-bill-wave"></i> Manage Fees
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('logout') }}">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>
        
        <!-- Main Content -->
        <main class="main-content">
            <div class="top-bar">
                <button class="menu-toggle" id="menu-toggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }}">
                            <i class="fas fa-{% if category == 'success' %}check-circle{% elif category == 'error' %}exclamation-circle{% elif category == 'warning' %}exclamation-triangle{% else %}info-circle{% endif %}"></i>
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
            
            <div class="content-area">
                <h2 style="color: #333; font-size: 1.5rem; margin-bottom: 10px;">Manage Department Fees</h2>
                <p style="color: #666; font-size: 1rem; margin-bottom: 30px;">select a faculty to manage its department</p>

                <div class="faculty-grid">
                    {% for faculty, departments in faculty_departments.items() %}
                    <div class="faculty-box">
                        <a href="{{ url_for('manage_faculty_departments', faculty=faculty | urlencode) }}">
                            {{ faculty.replace('Faculty of ', '').replace('faculty of ', '') }}
                        </a>
                    </div>
                    {% endfor %}
                </div>
            </div>

        </main>
    </div>

    <script>
        // Toggle sidebar on mobile
        const menuToggle = document.getElementById('menu-toggle');
        const sidebar = document.querySelector('.sidebar');
        
        menuToggle.addEventListener('click', function() {
            sidebar.classList.toggle('active');
        });
        
        // Close alerts after 5 seconds
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(alert => {
            setTimeout(() => {
                alert.style.display = 'none';
            }, 5000);
        });
    </script>
</body>
</html>

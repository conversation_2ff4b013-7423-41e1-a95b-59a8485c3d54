{% extends "student/base.html" %}

{% block title %}{{ translations.request_status }}{% endblock %}

{% block content %}
<style>
    .dashboard-header {
        background: white;
        padding: 30px;
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-bottom: 30px;
        text-align: center;
    }
    .dashboard-header h1 {
        color: #333;
        font-size: 2rem;
        margin-bottom: 10px;
        font-weight: 700;
    }
    .dashboard-header p {
        color: #666;
        font-size: 1.1rem;
        margin: 0;
    }
    .status-card {
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }
    .status-table {
        width: 100%;
        border-collapse: collapse;
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    .status-table th {
        background: #f8f9fa;
        color: #333;
        padding: 15px 12px;
        text-align: left;
        font-weight: 600;
        border-bottom: 2px solid #e9ecef;
        font-size: 0.9rem;
    }
    .status-table td {
        padding: 15px 12px;
        border-bottom: 1px solid #e9ecef;
        color: #495057;
        vertical-align: middle;
    }
    .status-table tr:hover {
        background: #f8f9fa;
    }
    .status-icon {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        margin-right: 8px;
    }
    .status-pending {
        background: #fff3cd;
        color: #856404;
    }
    .status-approved {
        background: #d4edda;
        color: #155724;
    }
    .status-rejected {
        background: #f8d7da;
        color: #721c24;
    }
    .download-btn {
        background: #28a745;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 6px;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        font-size: 0.875rem;
        transition: all 0.3s ease;
    }
    .download-btn:hover {
        background: #1e7e34;
        color: white;
        text-decoration: none;
    }
    .download-btn img {
        width: 16px;
        height: 16px;
        margin-right: 6px;
    }
    .delete-btn {
        background: #dc3545;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 6px;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        font-size: 0.875rem;
        transition: all 0.3s ease;
        margin-left: 8px;
        cursor: pointer;
    }
    .delete-btn:hover {
        background: #c82333;
        color: white;
        text-decoration: none;
    }
    .delete-btn img {
        width: 16px;
        height: 16px;
        margin-right: 6px;
    }
    .action-buttons {
        display: flex;
        align-items: center;
        gap: 8px;
    }
    .rejection-reason {
        color: #721c24;
        font-style: italic;
        font-size: 0.9rem;
        max-width: 200px;
        word-wrap: break-word;
    }
    .no-requests {
        text-align: center;
        padding: 40px;
        color: #666;
    }
    .no-requests i {
        font-size: 3rem;
        color: #ccc;
        margin-bottom: 15px;
    }

    /* Simplified status display - icons only */

    /* Status icon styles - for status column only */
    .status-icon-only {
        width: 32px;
        height: 32px;
        border-radius: 4px;
        transition: all 0.3s ease;
        cursor: pointer;
        display: block;
        margin: 0 auto;
    }

    .status-icon-only:hover {
        transform: scale(1.1);
        box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    }

    .status-container {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .delete-icon-btn {
        background: #dc3545;
        color: white;
        border: none;
        padding: 6px 10px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 16px;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }

    .delete-icon-btn:hover {
        background: #c82333;
        transform: scale(1.05);
    }

    /* Status explanation section - matching dashboard style */
    .status-explanation {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-top: 30px;
        border-left: 4px solid #007bff;
    }

    .status-explanation h4 {
        color: #333;
        margin-bottom: 15px;
        font-size: 1.1rem;
        font-weight: 600;
    }

    .status-item {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        padding: 8px;
        background: white;
        border-radius: 6px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .status-item img {
        width: 24px;
        height: 24px;
        margin-right: 12px;
        flex-shrink: 0;
    }

    .status-item span {
        color: #666;
        font-size: 0.9rem;
        line-height: 1.3;
    }
</style>

<div class="dashboard-header">
    <h1>{{ translations.request_status }}</h1>
    <p>{{ translations.request_status_desc or 'Track your transcript request status' }}</p>
</div>

<div class="status-card">
    {% if requests %}
    <div class="table-responsive">
        <table class="status-table">
            <thead>
                <tr>
                    <th>{{ translations.request_id }}</th>
                    <th>{{ translations.academic_years }}</th>
                    <th>{{ translations.date }}</th>
                    <th>{{ translations.status }}</th>
                    <th>{{ translations.rejection_reason or 'Reason for Rejection' }}</th>
                    <th>{{ translations.action }}</th>
                </tr>
            </thead>
            <tbody>
                {% for request in requests %}
                <tr>
                    <td><strong>{{ request.id }}</strong></td>
                    <td>{{ request.academic_years|join(', ') if request.academic_years else 'N/A' }}</td>
                    <td>{{ request.date }}</td>
                    <td>
                        <div class="status-container" style="justify-content: center;">
                            {% if request.status == 'pending_finance' %}
                                <img src="{{ url_for('static', filename='images/13.png') }}" alt="Pending" class="status-icon-only" title="Pending">
                            {% elif request.status == 'approved_finance' or request.status == 'faculty_processing' %}
                                <img src="{{ url_for('static', filename='images/approved.png') }}" alt="Approved" class="status-icon-only" title="Approved">
                            {% elif request.status == 'done' %}
                                <img src="{{ url_for('static', filename='images/done.png') }}" alt="Downloaded" class="status-icon-only" title="Downloaded">
                            {% elif request.status == 'completed' %}
                                <img src="{{ url_for('static', filename='images/8.png') }}" alt="Ready for Download" class="status-icon-only" title="Ready for Download">
                            {% elif request.status == 'rejected' %}
                                <img src="{{ url_for('static', filename='images/rejected.png') }}" alt="Rejected" class="status-icon-only" title="Rejected">
                            {% else %}
                                <img src="{{ url_for('static', filename='images/13.png') }}" alt="Processing" class="status-icon-only" title="Processing">
                            {% endif %}
                        </div>
                    </td>
                    <td>
                        {% if request.status == 'rejected' and request.rejection_reason %}
                            <div class="rejection-reason">{{ request.rejection_reason }}</div>
                        {% else %}
                            <span style="color: #999;">-</span>
                        {% endif %}
                    </td>
                    <td>
                        <div class="action-buttons">
                            {% if request.status == 'completed' %}
                                <!-- Download button for completed requests that haven't been downloaded -->
                                <a href="{{ url_for('view_downloads') }}" class="download-btn" title="View Downloads">
                                    📥 Download
                                </a>
                            {% endif %}

                            {% if request.status in ['pending_finance', 'rejected', 'done'] %}
                                <!-- Delete button for pending, rejected, and downloaded requests -->
                                <form method="POST" action="{{ url_for('student_delete_request', request_id=request.id) }}"
                                      style="display: inline;"
                                      onsubmit="return confirm('Are you sure you want to delete this request? This action cannot be undone.');">
                                    <button type="submit" class="delete-icon-btn" title="Delete Request">
                                        🗑️
                                    </button>
                                </form>
                            {% endif %}

                            {% if request.status not in ['completed', 'pending_finance', 'rejected', 'done'] %}
                                <span style="color: #999;">-</span>
                            {% endif %}
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- Status Icons Explanation - Same as Dashboard -->
    <div class="status-explanation">
        <h4>{{ translations.status_icons_explanation or 'Status Icons Explanation' }}</h4>
        <div class="status-item">
            <img src="{{ url_for('static', filename='images/13.png') }}" alt="Pending">
            <span>{{ translations.pending_explanation or 'Pending - Request is being processed by finance' }}</span>
        </div>
        <div class="status-item">
            <img src="{{ url_for('static', filename='images/approved.png') }}" alt="Approved">
            <span>{{ translations.approved_explanation or 'Approved - Request has been approved and sent to faculty' }}</span>
        </div>
        <div class="status-item">
            <img src="{{ url_for('static', filename='images/rejected.png') }}" alt="Rejected">
            <span>{{ translations.rejected_explanation or 'Rejected - Request has been rejected by finance' }}</span>
        </div>
        <div class="status-item">
            <img src="{{ url_for('static', filename='images/8.png') }}" alt="Ready for Download">
            <span>Ready for Download - Your transcript is ready to download (will be removed after download)</span>
        </div>
    </div>
    {% else %}
    <div class="no-requests">
        <i class="fas fa-inbox"></i>
        <p>{{ translations.no_requests }}</p>
        <a href="{{ url_for('request_transcript') }}" class="download-btn" style="margin-top: 15px;">
            <i class="fas fa-plus" style="margin-right: 6px;"></i>
            {{ translations.request_transcript }}
        </a>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
<script>
// Auto-refresh functionality for real-time status updates
let refreshInterval;
let lastUpdateTime = Date.now();

function refreshRequestStatus() {
    // Only refresh if page is visible and user is active
    if (document.hidden) return;

    fetch(window.location.href, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.text())
    .then(html => {
        // Parse the response to get the table content
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        const newTable = doc.querySelector('.status-table tbody');
        const currentTable = document.querySelector('.status-table tbody');

        if (newTable && currentTable) {
            // Check if content has changed
            if (newTable.innerHTML !== currentTable.innerHTML) {
                // Update the table with animation
                currentTable.style.opacity = '0.5';
                setTimeout(() => {
                    currentTable.innerHTML = newTable.innerHTML;
                    currentTable.style.opacity = '1';

                    // Highlight updated rows
                    const rows = currentTable.querySelectorAll('tr');
                    rows.forEach(row => {
                        row.classList.add('status-updated');
                        setTimeout(() => row.classList.remove('status-updated'), 2000);
                    });

                    console.log('Request status updated at', new Date().toLocaleTimeString());
                }, 300);
            }
        }
    })
    .catch(error => {
        console.log('Status refresh failed:', error);
    });
}

function startAutoRefresh() {
    // Refresh every 30 seconds
    refreshInterval = setInterval(refreshRequestStatus, 30000);
    console.log('Auto-refresh started for request status');
}

function stopAutoRefresh() {
    if (refreshInterval) {
        clearInterval(refreshInterval);
        refreshInterval = null;
        console.log('Auto-refresh stopped');
    }
}

// Start auto-refresh when page loads
document.addEventListener('DOMContentLoaded', function() {
    startAutoRefresh();

    // Stop refresh when page is hidden, restart when visible
    document.addEventListener('visibilitychange', function() {
        if (document.hidden) {
            stopAutoRefresh();
        } else {
            startAutoRefresh();
        }
    });

    // Manual refresh button
    const refreshBtn = document.createElement('button');
    refreshBtn.innerHTML = '<i class="fas fa-sync-alt"></i> Refresh Status';
    refreshBtn.className = 'btn btn-sm btn-outline-primary';
    refreshBtn.style.marginLeft = '10px';
    refreshBtn.onclick = function() {
        this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Refreshing...';
        refreshRequestStatus();
        setTimeout(() => {
            this.innerHTML = '<i class="fas fa-sync-alt"></i> Refresh Status';
        }, 1000);
    };

    // Add refresh button to header
    const header = document.querySelector('.dashboard-header');
    if (header) {
        header.appendChild(refreshBtn);
    }
});

// Clean up on page unload
window.addEventListener('beforeunload', stopAutoRefresh);
</script>
{% endblock %}

{% extends "base.html" %}

{% block content %}
<div class="dashboard-container">
    <!-- Sidebar -->
    <aside class="sidebar">
        <div class="sidebar-header">
            <img src="{{ url_for('static', filename='images/user.jpeg') }}" alt="Admin Profile" class="profile-image">
            <h2>{{ session.name }}</h2>
            <p>Administrator</p>
        </div>
        
        <nav class="sidebar-menu">
            <ul>
                <li>
                    <a href="{{ url_for('admin_dashboard') }}">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                </li>
                <li>
                    <a href="{{ url_for('manage_department_fees') }}" class="active">
                        <i class="fas fa-money-bill-wave"></i> Department Fees
                    </a>
                </li>
                <li>
                    <a href="{{ url_for('logout') }}">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                </li>
            </ul>
        </nav>
    </aside>

    <!-- Main Content -->
    <main class="main-content">
        <div class="top-bar">
            <button class="menu-toggle" id="menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
        </div>
        
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }}">
                        <i class="fas fa-{% if category == 'success' %}check-circle{% elif category == 'error' %}exclamation-circle{% elif category == 'warning' %}exclamation-triangle{% else %}info-circle{% endif %}"></i>
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <div class="dashboard-header">
            <h1>Manage Department Fees</h1>
            <p>Update annual fees for each department</p>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h2>Current Department Fees</h2>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table>
                        <thead>
                            <tr>
                                <th>Department</th>
                                <th>Annual Fee (RWF)</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for department, fee in department_fees.items() %}
                                <tr>
                                    <td>{{ department.replace('_', ' ').title() }}</td>
                                    <td>{{ "{:,}".format(fee) }}</td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-primary" onclick="showEditModal('{{ department }}', '{{ fee }}')">
                                            <i class="fas fa-edit"></i> Edit
                                        </button>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>
</div>

<!-- Edit Fee Modal -->
<div id="editModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h2>Edit Department Fee</h2>
            <button type="button" class="close" onclick="closeEditModal()">&times;</button>
        </div>
        <form id="editForm" action="{{ url_for('manage_department_fees') }}" method="POST">
            <input type="hidden" name="department" id="edit_department">
            <div class="form-group">
                <label>Department</label>
                <p id="edit_department_display" style="font-weight: 500;"></p>
            </div>
            
            <div class="form-group">
                <label for="edit_fee">Annual Fee (RWF)</label>
                <input type="number" id="edit_fee" name="fee" required min="0" step="1000">
            </div>
            
            <div class="form-group" style="margin-top: 20px; text-align: right;">
                <button type="button" class="btn btn-secondary" onclick="closeEditModal()">Cancel</button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Save Changes
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// Toggle sidebar on mobile
const menuToggle = document.getElementById('menu-toggle');
const sidebar = document.querySelector('.sidebar');

menuToggle.addEventListener('click', function() {
    sidebar.classList.toggle('active');
});

// Close alerts after 5 seconds
const alerts = document.querySelectorAll('.alert');
alerts.forEach(alert => {
    setTimeout(() => {
        alert.style.display = 'none';
    }, 5000);
});

// Edit modal functionality
const editModal = document.getElementById('editModal');
const editForm = document.getElementById('editForm');

function showEditModal(department, currentFee) {
    document.getElementById('edit_department').value = department;
    document.getElementById('edit_department_display').textContent = department.replace('_', ' ').title();
    document.getElementById('edit_fee').value = parseFloat(currentFee);
    editModal.style.display = 'block';
}

function closeEditModal() {
    editModal.style.display = 'none';
}

// Close modal when clicking outside
window.onclick = function(event) {
    if (event.target == editModal) {
        closeEditModal();
    }
}
</script>
{% endblock %} 
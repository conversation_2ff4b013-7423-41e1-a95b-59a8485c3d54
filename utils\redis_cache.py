"""
Redis cache utilities for INES Transcript System
Optional Redis integration
"""

def init_redis(app):
    """Initialize Redis if available"""
    try:
        from redis_service import init_redis_service
        init_redis_service(
            host=app.config['REDIS_HOST'],
            port=app.config['REDIS_PORT'],
            db=app.config['REDIS_DB'],
            password=app.config['REDIS_PASSWORD']
        )
        print("Redis initialized successfully")
    except ImportError:
        print("Redis not available - continuing without cache")
    except Exception as e:
        print(f"Redis initialization warning: {e}")

def get_redis_service():
    """Get Redis service if available"""
    try:
        from redis_service import get_redis_service
        return get_redis_service()
    except ImportError:
        return None
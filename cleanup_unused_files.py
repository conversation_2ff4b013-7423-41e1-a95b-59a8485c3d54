"""
Clean up unused files to reduce confusion
"""
import os
import sys

def identify_files_to_remove():
    """Identify files that can be safely removed"""
    
    # Files that are definitely not needed (test files, debug files, old versions)
    files_to_remove = [
        # Test files
        'test_academic_year_fix.py',
        'test_academic_years.py',
        'test_app_simple.py',
        'test_auth_fix.py',
        'test_available_years.py',
        'test_complete_faculty_workflow.py',
        'test_complete_system.py',
        'test_complete_system_workflow.py',
        'test_complete_workflow_fixed.py',
        'test_departments_fix.py',
        'test_download_fixed.py',
        'test_download_functionality.py',
        'test_duplicate_protection.py',
        'test_email_field_error.py',
        'test_email_fix.py',
        'test_email_notifications.py',
        'test_email_system.py',
        'test_fee_changes.py',
        'test_final_form_fix.py',
        'test_finance_panel.py',
        'test_finance_requests.py',
        'test_fixes.py',
        'test_login.py',
        'test_multiple_years.py',
        'test_payment_validation.py',
        'test_request_creation.py',
        'test_requests_function.py',
        'test_system_integration.py',
        'test_transcript_flow.py',
        'test_transcript_workflow.py',
        'test_user_experience.py',
        'test_years.py',
        'basic_test.py',
        'comprehensive_test.py',
        'final_system_test.py',
        'final_system_verification.py',
        'final_workflow_verification_complete.py',
        'simple_test.py',
        'system_test.py',
        'system_verification.py',
        'workflow_test.py',
        
        # Debug files
        'debug_academic_year_selection.py',
        'debug_database_columns.py',
        'debug_departments.py',
        'debug_download.py',
        'debug_duplicate_requests.py',
        'debug_faculty_requests.py',
        'debug_faculty_upload_completion.py',
        'debug_faculty_upload_status.py',
        'debug_finance.py',
        'debug_finance_request_issue.py',
        'debug_form_submission_issue.py',
        'debug_tables.py',
        'debug_validation.py',
        
        # Check files
        'check_actual_tables.py',
        'check_completed_requests.py',
        'check_current_database.py',
        'check_database.py',
        'check_db_structure.py',
        'check_faculty_credentials.py',
        'check_foreign_keys.py',
        'check_missing_transcripts.py',
        'check_payment_data.py',
        'check_request_status_mapping.py',
        'check_student_payments.py',
        'check_table_structure.py',
        
        # Old/backup versions
        'app_optimized.py',
        'app_refactored.py',
        'simple_database_service_backup.py',
        'simple_database_service_fixed.py',
        'simple_database_service_old.py',
        'simple_database_service_updated.py',
        'new_database_service.py',
        'new_database_service_optimized.py',
        'updated_database_service.py',
        'enhanced_finance_service.py',
        
        # Setup/migration files (keep for reference but not needed for operation)
        'add_missing_table.sql',
        'add_sample_data.sql',
        'add_sample_students.py',
        'backup_database.py',
        'clean_and_setup.sql',
        'clean_optimize.sql',
        'cleanup_database.sql',
        'create_payments_table.sql',
        'create_test_approved_request.py',
        'database_backup.sql',
        'database_migrations.py',
        'database_optimization.sql',
        'database_optimization_fixed.sql',
        'database_updates.sql',
        'final_optimize.sql',
        'fix_correct_passwords.sql',
        'fix_database_structure.sql',
        'fix_faculty_names.py',
        'fix_faculty_upload_status.py',
        'fix_foreign_key.py',
        'fix_passwords.py',
        'fix_passwords.sql',
        'fix_payment_method.py',
        'force_optimize.sql',
        'insert_sample_data.sql',
        'migrate.py',
        'optimize_database.py',
        'optimize_database.sql',
        'rename_column_and_fix_fees.py',
        'run_database_fixes.py',
        'run_optimization.py',
        'safe_cleanup.sql',
        'sample_data_generator.py',
        'setup_database.sql',
        'setup_test_data.py',
        'update_database_schema.py',
        'update_database_service.py',
        'update_real_departments.py',
        'update_student_enrollment_data.py',
        'verify_database_setup.py',
        
        # Other utility files
        'email_fix.py',
        'enhanced_auth_system.py',
        'role_based_dashboard.py',
        'user_management_system.py',
        'view_downloads_updated.py',
        
        # HTML files that are not used
        'homepage.html',
        'index.html',
        'login.html',
        'homepage-script.js',
        'homepage-styles.css',
        'styles.css',
        
        # Documentation files (keep some, remove duplicates)
        'ACADEMIC_YEARS_FEATURE.md',
        'CLEANUP_SUMMARY.md',
        'COLUMN_RENAME_AND_FEES_SUMMARY.md',
        'DATABASE_ANALYSIS_REPORT.md',
        'DATABASE_FIXES_SUMMARY.md',
        'DESIGN_CONSISTENCY_FIXES.md',
        'DOWNLOAD_BEHAVIOR_UPDATE.md',
        'FINANCE_PANEL_FIXES_SUMMARY.md',
        'FINANCE_SYSTEM_REVIEW.md',
        'FIXES_SUMMARY.md',
        'IMPLEMENTATION_SUMMARY.md',
        'OPTIMIZATION_IMPLEMENTATION_PLAN.md',
        'README_REFACTORED.md',
        'SIMPLIFIED_FINANCE_APPROACH.md',
        'SYSTEM_IMPROVEMENTS.md',
        'SYSTEM_IMPROVEMENTS_SUMMARY.md',
        'SYSTEM_READY.md',
        'TRANSCRIPT_FLOW_GUIDE.md',
        'TRANSCRIPT_SYSTEM_FIXES.md',
        'system_refinement_report.md',
        
        # Credential files
        'faculty_credentials.txt',
        'faculty_credentials_updated.txt',
        'login_credentials.md',
        
        # Other files
        'requirements_mysql.txt',  # Keep requirements.txt
        '-p'  # This seems to be a stray file
    ]
    
    return files_to_remove

def remove_files():
    """Remove the identified files"""
    files_to_remove = identify_files_to_remove()
    
    removed_count = 0
    failed_count = 0
    
    print("🧹 Cleaning up unused files...")
    print(f"📊 Found {len(files_to_remove)} files to remove")
    
    for file_path in files_to_remove:
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                print(f"✅ Removed: {file_path}")
                removed_count += 1
            else:
                print(f"⚠️  Not found: {file_path}")
        except Exception as e:
            print(f"❌ Failed to remove {file_path}: {e}")
            failed_count += 1
    
    print(f"\n📊 Cleanup Summary:")
    print(f"   ✅ Removed: {removed_count} files")
    print(f"   ❌ Failed: {failed_count} files")
    print(f"   ⚠️  Not found: {len(files_to_remove) - removed_count - failed_count} files")
    
    return removed_count

def list_remaining_files():
    """List the remaining important files"""
    print("\n📁 REMAINING IMPORTANT FILES:")
    print("=" * 40)
    
    # Core application files
    core_files = [
        'app.py',
        'simple_database_service.py',
        'notification_service.py',
        'simple_academic_years.py',
        'redis_service.py',
        'redis_session.py',
        'translations.py',
        'config.py'
    ]
    
    print("\n🔧 Core Application Files:")
    for file in core_files:
        if os.path.exists(file):
            print(f"   ✅ {file}")
        else:
            print(f"   ❌ {file} (MISSING)")
    
    # Template directories
    template_dirs = ['templates/student', 'templates/finance', 'templates/faculty']
    print("\n📄 Template Directories:")
    for dir_path in template_dirs:
        if os.path.exists(dir_path):
            files = os.listdir(dir_path)
            print(f"   ✅ {dir_path} ({len(files)} files)")
        else:
            print(f"   ❌ {dir_path} (MISSING)")
    
    # Static directories
    static_dirs = ['static/css', 'static/uploads']
    print("\n🎨 Static Directories:")
    for dir_path in static_dirs:
        if os.path.exists(dir_path):
            files = os.listdir(dir_path)
            print(f"   ✅ {dir_path} ({len(files)} files)")
        else:
            print(f"   ❌ {dir_path} (MISSING)")
    
    # Documentation
    important_docs = ['README.md', 'DEPLOYMENT_GUIDE.md', 'LOGIN_GUIDE.md']
    print("\n📚 Documentation:")
    for doc in important_docs:
        if os.path.exists(doc):
            print(f"   ✅ {doc}")
        else:
            print(f"   ❌ {doc} (MISSING)")

def main():
    """Main cleanup function"""
    print("🧹 INES Transcript System - File Cleanup")
    print("=" * 45)
    
    # Remove unused files
    removed_count = remove_files()
    
    # List remaining files
    list_remaining_files()
    
    print(f"\n🎉 CLEANUP COMPLETE!")
    print(f"   Removed {removed_count} unused files")
    print(f"   Project is now cleaner and less confusing")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

"""
Department Academic Years Service
Manages academic year availability based on department program duration
"""

import pymysql
import json
from datetime import datetime
from contextlib import contextmanager

DB_CONFIG = {
    'host': 'localhost',
    'user': 'ines_app',
    'password': 'ines_secure_2025!',
    'database': 'ines_transcript_system',
    'charset': 'utf8mb4'
}

@contextmanager
def get_db_connection():
    connection = pymysql.connect(**DB_CONFIG)
    try:
        yield connection
    finally:
        connection.close()

# Department program durations (in years)
DEPARTMENT_PROGRAM_DURATIONS = {
    # Faculty of Sciences and Information Technology (3 years)
    'Computer Science': 3,
    'Information Technology': 3,
    'Mathematics': 3,
    'Physics': 3,
    'Chemistry': 3,
    'Biology': 3,
    
    # Faculty of Economics Social Sciences and Management (3 years)
    'Economics': 3,
    'Business Administration': 3,
    'Management': 3,
    'Accounting': 3,
    'Finance': 3,
    'Marketing': 3,
    
    # Faculty of Engineering and Technology (4 years)
    'Civil Engineering': 4,
    'Electrical Engineering': 4,
    'Mechanical Engineering': 4,
    'Computer Engineering': 4,
    'Environmental Engineering': 4,
    
    # Faculty of Health Sciences (4 years)
    'Nursing': 4,
    'Public Health': 4,
    'Medical Laboratory Sciences': 4,
    'Pharmacy': 4,
    
    # Faculty of Law (4 years)
    'Law': 4,
    'Legal Studies': 4,
    
    # Faculty of Education (3 years)
    'Primary Education': 3,
    'Secondary Education': 3,
    'Educational Psychology': 3,
    'Curriculum and Instruction': 3
}

def get_department_program_duration(department_name):
    """Get program duration for a department"""
    return DEPARTMENT_PROGRAM_DURATIONS.get(department_name, 3)  # Default to 3 years

def get_student_available_academic_years(student_reg_no):
    """Get available academic years for a student based on their department and enrollment"""
    try:
        with get_db_connection() as connection:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # Get student information from simplified users table
                cursor.execute("""
                    SELECT department, enrollment_start_year, current_year_level
                    FROM users
                    WHERE reg_no = %s AND role = 'student'
                """, (student_reg_no,))
                
                student = cursor.fetchone()
                if not student:
                    return []
                
                department = student['department']
                enrollment_start = student.get('enrollment_start_year', 2021)  # Default if null
                current_year_level = student.get('current_year_level', 1) or 1  # Default if null
                
                # Get program duration for this department
                program_duration = get_department_program_duration(department)
                
                # Calculate available years
                available_years = []
                
                # Generate academic years based on enrollment start and program duration
                for year_level in range(1, program_duration + 1):
                    academic_year = enrollment_start + year_level - 1
                    
                    # Only include completed years (not current year being studied)
                    if year_level < current_year_level:
                        # Format as academic year (e.g., "2021-2022")
                        academic_year_str = f"{academic_year}-{academic_year + 1}"
                        available_years.append(academic_year_str)
                
                return available_years
                
    except Exception as e:
        print(f"Error getting available academic years: {e}")
        return []

def validate_academic_year_request(student_reg_no, requested_year):
    """Validate if student can request transcript for a specific academic year"""
    available_years = get_student_available_academic_years(student_reg_no)
    return requested_year in available_years

def get_current_academic_year():
    """Get current academic year string"""
    current_year = datetime.now().year
    # Academic year typically starts in September, so adjust accordingly
    if datetime.now().month >= 9:  # September or later
        return f"{current_year}-{current_year + 1}"
    else:  # Before September
        return f"{current_year - 1}-{current_year}"

def is_current_academic_year(academic_year_str):
    """Check if the given academic year is the current one"""
    current_year = get_current_academic_year()
    return academic_year_str == current_year

def update_student_year_level(student_reg_no, new_year_level):
    """Update student's current year level"""
    try:
        with get_db_connection() as connection:
            with connection.cursor() as cursor:
                cursor.execute("""
                    UPDATE users 
                    SET current_year_level = %s
                    WHERE reg_no = %s AND role = 'student'
                """, (new_year_level, student_reg_no))
                
                connection.commit()
                return cursor.rowcount > 0
                
    except Exception as e:
        print(f"Error updating student year level: {e}")
        return False

def get_department_academic_info(department_name):
    """Get academic information for a department"""
    program_duration = get_department_program_duration(department_name)
    
    return {
        'department': department_name,
        'program_duration': program_duration,
        'program_type': f"{program_duration}-year program",
        'typical_years': [f"Year {i}" for i in range(1, program_duration + 1)]
    }

def get_all_departments_academic_info():
    """Get academic information for all departments"""
    departments_info = {}
    
    for dept, duration in DEPARTMENT_PROGRAM_DURATIONS.items():
        departments_info[dept] = get_department_academic_info(dept)
    
    return departments_info

def initialize_student_enrollment_years(student_reg_no, department_name, enrollment_start_year, current_year_level=1):
    """Initialize enrollment years for a new student"""
    try:
        program_duration = get_department_program_duration(department_name)
        
        # Generate all possible academic years for this student
        all_years = []
        for year_level in range(1, program_duration + 1):
            academic_year = enrollment_start_year + year_level - 1
            academic_year_str = f"{academic_year}-{academic_year + 1}"
            all_years.append(academic_year_str)
        
        with get_db_connection() as connection:
            with connection.cursor() as cursor:
                cursor.execute("""
                    UPDATE users 
                    SET enrollment_years = %s,
                        current_year_level = %s,
                        program_duration = %s
                    WHERE reg_no = %s AND role = 'student'
                """, (json.dumps(all_years), current_year_level, program_duration, student_reg_no))
                
                connection.commit()
                return cursor.rowcount > 0
                
    except Exception as e:
        print(f"Error initializing student enrollment years: {e}")
        return False

# Compatibility functions for existing code
def get_student_enrollment_years(student_reg_no):
    """Get available academic years (completed only)"""
    return get_student_available_academic_years(student_reg_no)

def validate_student_year_request(student_reg_no, academic_year):
    """Validate year request (compatibility function)"""
    return validate_academic_year_request(student_reg_no, academic_year)
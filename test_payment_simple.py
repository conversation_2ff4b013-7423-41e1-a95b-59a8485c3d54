#!/usr/bin/env python3
"""
Simple test to verify payment error fix
"""

from services.momo_api import momo_api
import os

def test_momo_api():
    """Test the MoMo API directly"""
    print("🧪 Testing MoMo API Error Fix")
    print("=" * 40)
    
    # Test phone number
    phone_number = "250788123456"
    amount = 100
    
    print(f"📱 Testing payment for {phone_number} with amount {amount} RWF")
    print(f"🔧 Current test mode: {momo_api.test_mode}")
    
    try:
        # Test payment initiation
        print("1. Testing payment initiation...")
        result = momo_api.request_to_pay(phone_number, amount)
        
        print(f"Payment result: {result}")
        
        if result and result.get('success'):
            print("✅ Payment initiation successful!")
            print(f"Transaction ID: {result.get('transaction_id')}")
            print(f"Message: {result.get('message')}")
            
            # Test payment status check
            print("\n2. Testing payment status check...")
            transaction_id = result.get('transaction_id')
            status_result = momo_api.check_payment_status(transaction_id)
            
            print(f"Status result: {status_result}")
            
            if status_result:
                print("✅ Payment status check successful!")
                print(f"Status: {status_result.get('status')}")
            else:
                print("❌ Payment status check failed")
                
        else:
            print("❌ Payment initiation failed")
            print(f"Error: {result.get('message') if result else 'No result returned'}")
            
    except Exception as e:
        print(f"❌ Exception occurred: {e}")
        import traceback
        traceback.print_exc()
        
    print("\n" + "=" * 40)
    print("🎯 MoMo API Test Completed")

if __name__ == "__main__":
    test_momo_api()

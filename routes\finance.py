"""
Finance routes for INES Transcript System
Finance dashboard and request management
"""
from flask import Blueprint, render_template, request, redirect, url_for, flash
from utils.decorators import login_required, finance_required
from services.finance_service import (
    get_finance_dashboard_data,
    approve_request,
    reject_request,
    delete_request
)

finance_bp = Blueprint('finance', __name__)

@finance_bp.route('/dashboard')
@login_required
@finance_required
def dashboard():
    """Finance dashboard"""
    dashboard_data = get_finance_dashboard_data()
    return render_template('finance/enhanced_dashboard_new.html', **dashboard_data)

@finance_bp.route('/view-status')
@login_required
@finance_required
def view_status():
    """View pending requests"""
    dashboard_data = get_finance_dashboard_data()
    return render_template('finance/view_status_new.html', 
                         pending_requests=dashboard_data['pending_requests'])

@finance_bp.route('/view-history')
@login_required
@finance_required
def view_history():
    """View processed requests"""
    dashboard_data = get_finance_dashboard_data()
    return render_template('finance/view_history_new.html',
                         approved_requests=dashboard_data['approved_requests'],
                         rejected_requests=dashboard_data['rejected_requests'])

@finance_bp.route('/approve-request', methods=['POST'])
@login_required
@finance_required
def approve_request_route():
    """Approve a transcript request"""
    request_id = request.form.get('request_id')
    if not request_id:
        flash('Invalid request ID', 'error')
        return redirect(url_for('finance.view_status'))
    
    success = approve_request(request_id, session['user_id'])
    if success:
        flash('Request approved successfully', 'success')
    else:
        flash('Error approving request', 'error')
    
    return redirect(url_for('finance.view_status'))

@finance_bp.route('/reject-request', methods=['POST'])
@login_required
@finance_required
def reject_request_route():
    """Reject a transcript request"""
    request_id = request.form.get('request_id')
    rejection_reason = request.form.get('rejection_reason')
    
    if not request_id:
        flash('Invalid request ID', 'error')
        return redirect(url_for('finance.view_status'))
    
    success = reject_request(request_id, session['user_id'], rejection_reason)
    if success:
        flash('Request rejected successfully', 'success')
    else:
        flash('Error rejecting request', 'error')
    
    return redirect(url_for('finance.view_status'))

@finance_bp.route('/delete-request/<int:request_id>', methods=['POST'])
@login_required
@finance_required
def delete_request_route(request_id):
    """Delete a processed request"""
    success = delete_request(request_id)
    if success:
        flash('Request deleted successfully', 'success')
    else:
        flash('Error deleting request', 'error')
    
    return redirect(url_for('finance.view_history'))
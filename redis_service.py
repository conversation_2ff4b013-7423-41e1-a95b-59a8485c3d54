"""
Redis Service for Session Management and Caching
Provides centralized Redis operations for the INES Transcript System
"""

import redis
import json
import pickle
import os
from datetime import datetime, timedelta
from typing import Any, Optional, Dict, List
from functools import wraps
import hashlib

class RedisService:
    """Redis service for session management and caching"""
    
    def __init__(self, host='localhost', port=6379, db=0, password=None):
        """Initialize Redis connection"""
        self.redis_client = redis.Redis(
            host=host,
            port=port,
            db=db,
            password=password,
            decode_responses=False,  # We'll handle encoding manually
            socket_connect_timeout=5,
            socket_timeout=5,
            retry_on_timeout=True,
            health_check_interval=30
        )
        
        # Test connection
        try:
            self.redis_client.ping()
            print("✅ Redis connection established successfully")
        except redis.ConnectionError as e:
            print(f"❌ Redis connection failed: {e}")
            print("💡 Make sure Redis server is running on localhost:6379")
            # Fall back to in-memory storage for development
            self._fallback_storage = {}
            self._use_fallback = True
        except Exception as e:
            print(f"❌ Redis initialization error: {e}")
            self._fallback_storage = {}
            self._use_fallback = True
        else:
            self._use_fallback = False
    
    def _serialize(self, data: Any) -> bytes:
        """Serialize data for Redis storage"""
        try:
            return pickle.dumps(data)
        except Exception as e:
            print(f"Serialization error: {e}")
            return pickle.dumps(str(data))
    
    def _deserialize(self, data: bytes) -> Any:
        """Deserialize data from Redis"""
        try:
            return pickle.loads(data)
        except Exception as e:
            print(f"Deserialization error: {e}")
            return None
    
    def _get_key(self, prefix: str, key: str) -> str:
        """Generate Redis key with prefix"""
        return f"ines:{prefix}:{key}"
    
    # Session Management
    def set_session(self, session_id: str, session_data: Dict, expire_seconds: int = 3600) -> bool:
        """Store session data in Redis"""
        try:
            if self._use_fallback:
                self._fallback_storage[f"session:{session_id}"] = {
                    'data': session_data,
                    'expires': datetime.now() + timedelta(seconds=expire_seconds)
                }
                return True
            
            key = self._get_key("session", session_id)
            serialized_data = self._serialize(session_data)
            return self.redis_client.setex(key, expire_seconds, serialized_data)
        except Exception as e:
            print(f"Error setting session {session_id}: {e}")
            return False
    
    def get_session(self, session_id: str) -> Optional[Dict]:
        """Retrieve session data from Redis"""
        try:
            if self._use_fallback:
                session_key = f"session:{session_id}"
                if session_key in self._fallback_storage:
                    session_info = self._fallback_storage[session_key]
                    if datetime.now() < session_info['expires']:
                        return session_info['data']
                    else:
                        del self._fallback_storage[session_key]
                return None
            
            key = self._get_key("session", session_id)
            data = self.redis_client.get(key)
            if data:
                return self._deserialize(data)
            return None
        except Exception as e:
            print(f"Error getting session {session_id}: {e}")
            return None
    
    def delete_session(self, session_id: str) -> bool:
        """Delete session from Redis"""
        try:
            if self._use_fallback:
                session_key = f"session:{session_id}"
                if session_key in self._fallback_storage:
                    del self._fallback_storage[session_key]
                return True
            
            key = self._get_key("session", session_id)
            return bool(self.redis_client.delete(key))
        except Exception as e:
            print(f"Error deleting session {session_id}: {e}")
            return False
    
    def extend_session(self, session_id: str, expire_seconds: int = 3600) -> bool:
        """Extend session expiration time"""
        try:
            if self._use_fallback:
                session_key = f"session:{session_id}"
                if session_key in self._fallback_storage:
                    self._fallback_storage[session_key]['expires'] = datetime.now() + timedelta(seconds=expire_seconds)
                    return True
                return False
            
            key = self._get_key("session", session_id)
            return bool(self.redis_client.expire(key, expire_seconds))
        except Exception as e:
            print(f"Error extending session {session_id}: {e}")
            return False
    
    # Data Caching
    def cache_set(self, key: str, data: Any, expire_seconds: int = 300) -> bool:
        """Cache data with expiration"""
        try:
            if self._use_fallback:
                self._fallback_storage[f"cache:{key}"] = {
                    'data': data,
                    'expires': datetime.now() + timedelta(seconds=expire_seconds)
                }
                return True
            
            cache_key = self._get_key("cache", key)
            serialized_data = self._serialize(data)
            return self.redis_client.setex(cache_key, expire_seconds, serialized_data)
        except Exception as e:
            print(f"Error caching data for key {key}: {e}")
            return False
    
    def cache_get(self, key: str) -> Any:
        """Retrieve cached data"""
        try:
            if self._use_fallback:
                cache_key = f"cache:{key}"
                if cache_key in self._fallback_storage:
                    cache_info = self._fallback_storage[cache_key]
                    if datetime.now() < cache_info['expires']:
                        return cache_info['data']
                    else:
                        del self._fallback_storage[cache_key]
                return None
            
            cache_key = self._get_key("cache", key)
            data = self.redis_client.get(cache_key)
            if data:
                return self._deserialize(data)
            return None
        except Exception as e:
            print(f"Error getting cached data for key {key}: {e}")
            return None
    
    def cache_delete(self, key: str) -> bool:
        """Delete cached data"""
        try:
            if self._use_fallback:
                cache_key = f"cache:{key}"
                if cache_key in self._fallback_storage:
                    del self._fallback_storage[cache_key]
                return True
            
            cache_key = self._get_key("cache", key)
            return bool(self.redis_client.delete(cache_key))
        except Exception as e:
            print(f"Error deleting cached data for key {key}: {e}")
            return False
    
    # Request Caching
    def cache_requests_for_student(self, student_id: str, requests: List[Dict], expire_seconds: int = 300) -> bool:
        """Cache student requests"""
        key = f"student_requests:{student_id}"
        return self.cache_set(key, requests, expire_seconds)
    
    def get_cached_requests_for_student(self, student_id: str) -> Optional[List[Dict]]:
        """Get cached student requests"""
        key = f"student_requests:{student_id}"
        return self.cache_get(key)
    
    def invalidate_student_cache(self, student_id: str) -> bool:
        """Invalidate all cache for a student"""
        keys_to_delete = [
            f"student_requests:{student_id}",
            f"student_stats:{student_id}",
            f"student_profile:{student_id}"
        ]
        
        success = True
        for key in keys_to_delete:
            if not self.cache_delete(key):
                success = False
        
        return success
    
    # Statistics Caching
    def cache_dashboard_stats(self, user_id: str, role: str, stats: Dict, expire_seconds: int = 600) -> bool:
        """Cache dashboard statistics"""
        key = f"dashboard_stats:{role}:{user_id}"
        return self.cache_set(key, stats, expire_seconds)
    
    def get_cached_dashboard_stats(self, user_id: str, role: str) -> Optional[Dict]:
        """Get cached dashboard statistics"""
        key = f"dashboard_stats:{role}:{user_id}"
        return self.cache_get(key)
    
    # Rate Limiting
    def check_rate_limit(self, identifier: str, limit: int, window_seconds: int) -> bool:
        """Check if request is within rate limit"""
        try:
            if self._use_fallback:
                # Simple fallback rate limiting
                rate_key = f"rate:{identifier}"
                now = datetime.now()
                if rate_key in self._fallback_storage:
                    rate_info = self._fallback_storage[rate_key]
                    if now - rate_info['start'] < timedelta(seconds=window_seconds):
                        if rate_info['count'] >= limit:
                            return False
                        rate_info['count'] += 1
                    else:
                        self._fallback_storage[rate_key] = {'start': now, 'count': 1}
                else:
                    self._fallback_storage[rate_key] = {'start': now, 'count': 1}
                return True
            
            key = self._get_key("rate", identifier)
            current = self.redis_client.get(key)
            
            if current is None:
                # First request in window
                self.redis_client.setex(key, window_seconds, 1)
                return True
            
            current_count = int(current)
            if current_count >= limit:
                return False
            
            # Increment counter
            self.redis_client.incr(key)
            return True
            
        except Exception as e:
            print(f"Error checking rate limit for {identifier}: {e}")
            return True  # Allow request on error
    
    # Health Check
    def health_check(self) -> Dict[str, Any]:
        """Check Redis health and return status"""
        try:
            if self._use_fallback:
                return {
                    'status': 'fallback',
                    'message': 'Using in-memory fallback storage',
                    'redis_available': False,
                    'fallback_keys': len(self._fallback_storage)
                }
            
            # Test Redis operations
            test_key = "health_check_test"
            self.redis_client.set(test_key, "test", ex=10)
            value = self.redis_client.get(test_key)
            self.redis_client.delete(test_key)
            
            info = self.redis_client.info()
            
            return {
                'status': 'healthy',
                'message': 'Redis is working properly',
                'redis_available': True,
                'redis_version': info.get('redis_version'),
                'connected_clients': info.get('connected_clients'),
                'used_memory_human': info.get('used_memory_human'),
                'uptime_in_seconds': info.get('uptime_in_seconds')
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'message': f'Redis health check failed: {e}',
                'redis_available': False
            }

# Global Redis service instance
redis_service = None

def init_redis_service(host='localhost', port=6379, db=0, password=None):
    """Initialize global Redis service"""
    global redis_service
    redis_service = RedisService(host, port, db, password)
    return redis_service

def get_redis_service() -> RedisService:
    """Get global Redis service instance"""
    global redis_service
    if redis_service is None:
        redis_service = RedisService()
    return redis_service

# Decorator for caching function results
def cache_result(expire_seconds=300, key_prefix="func"):
    """Decorator to cache function results"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key from function name and arguments
            key_parts = [key_prefix, func.__name__]
            
            # Add args to key
            for arg in args:
                if isinstance(arg, (str, int, float)):
                    key_parts.append(str(arg))
                else:
                    key_parts.append(hashlib.md5(str(arg).encode()).hexdigest()[:8])
            
            # Add kwargs to key
            for k, v in sorted(kwargs.items()):
                key_parts.append(f"{k}:{v}")
            
            cache_key = ":".join(key_parts)
            
            # Try to get from cache
            redis_svc = get_redis_service()
            cached_result = redis_svc.cache_get(cache_key)
            
            if cached_result is not None:
                return cached_result
            
            # Execute function and cache result
            result = func(*args, **kwargs)
            redis_svc.cache_set(cache_key, result, expire_seconds)
            
            return result
        return wrapper
    return decorator

"""
Enhanced Notification Service
Handles all email notifications with proper error handling
"""
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON>ultipart
from datetime import datetime
import os
from dotenv import load_dotenv

load_dotenv()

# Email configuration
EMAIL_HOST = os.getenv('MAIL_SERVER', 'smtp.gmail.com')
EMAIL_PORT = int(os.getenv('MAIL_PORT', '587'))
EMAIL_USERNAME = os.getenv('MAIL_USERNAME')
EMAIL_PASSWORD = os.getenv('MAIL_PASSWORD')
EMAIL_FROM = os.getenv('MAIL_DEFAULT_SENDER')

def send_notification_email(to_email, subject, content):
    """Send email notification with proper error handling"""
    try:
        msg = MIMEMultipart()
        msg['From'] = EMAIL_FROM
        msg['To'] = to_email
        msg['Subject'] = subject
        
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background-color: #003366; color: white; padding: 20px; text-align: center; }}
                .content {{ padding: 20px; background-color: #f9f9f9; }}
                .footer {{ text-align: center; padding: 20px; font-size: 12px; color: #666; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>INES-Ruhengeri</h1>
                    <p>Transcript System</p>
                </div>
                <div class="content">
                    {content}
                </div>
                <div class="footer">
                    <p>© {datetime.now().year} INES-Ruhengeri. All rights reserved.</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        msg.attach(MIMEText(html_content, 'html'))
        
        with smtplib.SMTP(EMAIL_HOST, EMAIL_PORT) as server:
            server.starttls()
            server.login(EMAIL_USERNAME, EMAIL_PASSWORD)
            server.send_message(msg)
        
        print(f"✅ Email sent to {to_email}")
        return True
    except Exception as e:
        print(f"❌ Email failed to {to_email}: {e}")
        return False

def notify_finance_new_request(request_data):
    """Notify finance about new request"""
    content = f"""
    <h2>🆕 New Transcript Request</h2>
    <p>A new transcript request requires your review:</p>
    <ul>
        <li><strong>Request ID:</strong> {request_data.get('id')}</li>
        <li><strong>Student:</strong> {request_data.get('student_name')}</li>
        <li><strong>Department:</strong> {request_data.get('department')}</li>
        <li><strong>Years:</strong> {', '.join(request_data.get('academic_years', []))}</li>
        <li><strong>Amount:</strong> {request_data.get('total_price', 0):,.0f} RWF</li>
    </ul>
    <p>Please review and approve/reject in the finance dashboard.</p>
    """
    
    from simple_database_service import get_department_emails
    finance_emails = get_department_emails('finance')
    if not finance_emails:
        finance_emails = ['<EMAIL>']  # Fallback
    
    for email in finance_emails:
        send_notification_email(email, 'New Transcript Request - INES', content)

def notify_student_approval(request_data):
    """Notify student about approval"""
    content = f"""
    <h2>🎉 Request Approved!</h2>
    <p>Dear {request_data.get('student_name')},</p>
    <p>Your transcript request has been approved and sent to faculty for processing.</p>
    <ul>
        <li><strong>Request ID:</strong> {request_data.get('id')}</li>
        <li><strong>Years:</strong> {', '.join(request_data.get('academic_years', []))}</li>
        <li><strong>Status:</strong> Approved - Sent to Faculty</li>
    </ul>
    <p>You will receive another notification when your transcript is ready.</p>
    """
    
    student_email = request_data.get('email') or request_data.get('student_email')
    if student_email:
        send_notification_email(student_email, 'Transcript Request Approved - INES', content)

def notify_student_rejection(request_data, reason):
    """Notify student about rejection"""
    content = f"""
    <h2>📋 Request Update</h2>
    <p>Dear {request_data.get('student_name')},</p>
    <p>Your transcript request has been reviewed and cannot be processed at this time.</p>
    <ul>
        <li><strong>Request ID:</strong> {request_data.get('id')}</li>
        <li><strong>Reason:</strong> {reason}</li>
    </ul>
    <p>Please resolve the issue and submit a new request.</p>
    """
    
    student_email = request_data.get('email') or request_data.get('student_email')
    if student_email:
        send_notification_email(student_email, 'Transcript Request Update - INES', content)

def notify_faculty_new_request(request_data):
    """Notify faculty about approved request"""
    content = f"""
    <h2>📋 New Approved Request</h2>
    <p>A transcript request has been approved and requires your processing:</p>
    <ul>
        <li><strong>Request ID:</strong> {request_data.get('id')}</li>
        <li><strong>Student:</strong> {request_data.get('student_name')}</li>
        <li><strong>Department:</strong> {request_data.get('department')}</li>
        <li><strong>Years:</strong> {', '.join(request_data.get('academic_years', []))}</li>
    </ul>
    <p>Please prepare and upload the transcript in the faculty dashboard.</p>
    """
    
    # Get faculty emails based on department
    department = request_data.get('department', '')
    faculty_email_map = {
        'Computer Science': ['<EMAIL>'],
        'Statistics Applied to Economy': ['<EMAIL>'],
        'Civil Engineering': ['<EMAIL>'],
        'Architecture': ['<EMAIL>'],
        'Law': ['<EMAIL>']
    }
    
    faculty_emails = faculty_email_map.get(department, [])
    for email in faculty_emails:
        send_notification_email(email, f'New Approved Request - {department} - INES', content)

def notify_student_transcript_ready(request_data):
    """Notify student when transcript is ready"""
    content = f"""
    <h2>🎉 Your Transcript is Ready!</h2>
    <p>Dear {request_data.get('student_name')},</p>
    <p>Your transcript has been processed and is ready for download.</p>
    <ul>
        <li><strong>Request ID:</strong> {request_data.get('id')}</li>
        <li><strong>Years:</strong> {', '.join(request_data.get('academic_years', []))}</li>
        <li><strong>Status:</strong> ✅ Ready for Download</li>
    </ul>
    <p>Login to your student dashboard and go to "View Downloads" to get your transcript.</p>
    """
    
    student_email = request_data.get('email') or request_data.get('student_email')
    if student_email:
        send_notification_email(student_email, 'Your Transcript is Ready - INES', content)
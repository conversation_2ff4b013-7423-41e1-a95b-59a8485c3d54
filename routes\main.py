"""
Main routes for INES Transcript System
Homepage, general pages, and redirects
"""
from flask import Blueprint, render_template, redirect, url_for

main_bp = Blueprint('main', __name__)

@main_bp.route('/')
def index():
    """Root redirect to homepage"""
    return redirect(url_for('main.homepage'))

@main_bp.route('/home')
def homepage():
    """Application homepage"""
    return render_template('homepage.html')

@main_bp.route('/login')
def login_redirect():
    """Redirect to auth login"""
    return redirect(url_for('auth.login'))
"""
Faculty routes for INES Transcript System
Faculty dashboard and transcript processing
"""
from flask import Blueprint, render_template, request, redirect, url_for, session, flash
from utils.decorators import login_required, faculty_required
from services.faculty_service import (
    get_faculty_dashboard_data,
    upload_transcript_files,
    get_faculty_requests
)

faculty_bp = Blueprint('faculty', __name__)

@faculty_bp.route('/dashboard')
@login_required
@faculty_required
def dashboard():
    """Faculty dashboard"""
    faculty_department = session.get('department')
    dashboard_data = get_faculty_dashboard_data(faculty_department)
    return render_template('faculty/dashboard.html', **dashboard_data)

@faculty_bp.route('/upload-transcript', methods=['GET', 'POST'])
@login_required
@faculty_required
def upload_transcript():
    """Upload transcript files"""
    faculty_department = session.get('department')
    
    if request.method == 'POST':
        request_id = request.form.get('request_id')
        
        if not request_id:
            flash('Invalid request ID', 'error')
            return redirect(url_for('faculty.upload_transcript'))
        
        # Handle file uploads
        files = []
        for key in request.files:
            if key.startswith('transcript'):
                file = request.files[key]
                if file and file.filename:
                    files.append(file)
        
        if not files:
            flash('Please upload at least one transcript file', 'error')
            return redirect(url_for('faculty.upload_transcript'))
        
        success = upload_transcript_files(request_id, files, faculty_department)
        if success:
            flash('Transcript uploaded successfully!', 'success')
        else:
            flash('Error uploading transcript', 'error')
        
        return redirect(url_for('faculty.upload_transcript'))
    
    # GET request - show pending requests
    requests = get_faculty_requests(faculty_department, status='approved_finance')
    return render_template('faculty/upload_transcript.html', 
                         requests=requests,
                         pending_count=len(requests))

@faculty_bp.route('/request-history')
@login_required
@faculty_required
def request_history():
    """View faculty request history"""
    faculty_department = session.get('department')
    completed_requests = get_faculty_requests(faculty_department, status='completed')
    pending_requests = get_faculty_requests(faculty_department, status='approved_finance')
    
    return render_template('faculty/request_history.html',
                         requests=completed_requests,
                         total_uploaded=len(completed_requests),
                         pending_count=len(pending_requests))
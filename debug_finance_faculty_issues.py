"""
Debug and fix finance and faculty issues:
1. Finance rejection not appearing in history
2. Faculty not receiving approved requests
3. Notification and statcard updates
"""
import sys

def test_finance_rejection_workflow():
    """Test the complete rejection workflow"""
    try:
        print("🔍 Testing finance rejection workflow...")
        
        from simple_database_service import get_all_requests, reject_transcript_request, get_finance_dashboard_data
        
        # Get initial state
        initial_requests = get_all_requests()
        initial_dashboard = get_finance_dashboard_data()
        
        print(f"📊 Initial state:")
        print(f"   - Total requests: {len(initial_requests)}")
        print(f"   - Pending: {len(initial_dashboard['pending_requests'])}")
        print(f"   - Rejected: {len(initial_dashboard['rejected_requests'])}")
        
        # Find a pending request to reject
        pending_requests = initial_dashboard['pending_requests']
        if pending_requests:
            test_request = pending_requests[0]
            request_id = test_request['id']
            
            print(f"\n📝 Testing rejection of request {request_id}")
            
            # Test rejection
            success = reject_transcript_request(request_id, 'FIN001', 'Test rejection reason')
            
            if success:
                print("✅ Rejection function returned success")
                
                # Check updated dashboard
                updated_dashboard = get_finance_dashboard_data()
                
                print(f"📊 After rejection:")
                print(f"   - Pending: {len(updated_dashboard['pending_requests'])}")
                print(f"   - Rejected: {len(updated_dashboard['rejected_requests'])}")
                
                # Check if request appears in rejected list
                rejected_ids = [req['id'] for req in updated_dashboard['rejected_requests']]
                
                if request_id in rejected_ids:
                    print("✅ Request appears in rejected list")
                    return True
                else:
                    print("❌ Request NOT in rejected list")
                    print(f"   Rejected IDs: {rejected_ids}")
                    return False
            else:
                print("❌ Rejection function failed")
                return False
        else:
            print("⚠️  No pending requests to test rejection")
            return True
            
    except Exception as e:
        print(f"❌ Error testing rejection: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_faculty_request_routing():
    """Test faculty request routing and department mapping"""
    try:
        print("\n🔍 Testing faculty request routing...")
        
        from simple_database_service import get_all_requests, approve_transcript_request
        import app
        
        # Get current requests
        all_requests = get_all_requests()
        pending_requests = [req for req in all_requests if req.get('payment_status') == 'pending']
        
        print(f"📊 Current state:")
        print(f"   - Total requests: {len(all_requests)}")
        print(f"   - Pending requests: {len(pending_requests)}")
        
        if pending_requests:
            test_request = pending_requests[0]
            request_id = test_request['id']
            
            print(f"\n📝 Testing approval of request {request_id}")
            print(f"   Student department: {test_request.get('department', 'Unknown')}")
            
            # Test approval
            success = approve_transcript_request(request_id, 'FIN001')
            
            if success:
                print("✅ Approval function returned success")
                
                # Test faculty dashboard access
                with app.app.test_client() as client:
                    with client.session_transaction() as sess:
                        sess['user_id'] = 'FAC001'
                        sess['role'] = 'faculty'
                        sess['email'] = '<EMAIL>'
                        sess['name'] = 'Faculty Member'
                        sess['department'] = 'Faculty of Sciences and Information Technology'
                    
                    # Test faculty dashboard
                    response = client.get('/faculty/dashboard')
                    
                    if response.status_code == 200:
                        print("✅ Faculty dashboard accessible")
                        
                        # Check faculty pending requests function
                        from app import get_pending_requests_for_faculty
                        faculty_requests = get_pending_requests_for_faculty()
                        
                        print(f"📊 Faculty sees {len(faculty_requests)} pending requests")
                        
                        # Check department mapping
                        for req in faculty_requests:
                            print(f"   - Request {req['id']}: {req.get('department', 'Unknown')}")
                        
                        return len(faculty_requests) > 0
                    else:
                        print(f"❌ Faculty dashboard failed: {response.status_code}")
                        return False
            else:
                print("❌ Approval function failed")
                return False
        else:
            print("⚠️  No pending requests to test approval")
            return True
            
    except Exception as e:
        print(f"❌ Error testing faculty routing: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_department_mapping():
    """Test department to faculty mapping"""
    try:
        print("\n🔍 Testing department to faculty mapping...")
        
        # Test the mapping function
        faculty_dept_map = {
            'Faculty of Sciences and Information Technology': ['Computer Science', 'Statistics Applied to Economy'],
            'Faculty of Engineering and Technology': ['Civil Engineering', 'Architecture', 'Biotechnologies', 'Water Engineering', 'Surveying', 'Land Survey', 'Masonry', 'Welding', 'Domestic Plumbing'],
            'Faculty of Economics Social Sciences and Management': ['Cooperatives Management', 'Entrepreneurship and SME\'s Management'],
            'Faculty of Education': ['French and English'],
            'Faculty of Law': ['Law']
        }
        
        def belongs_to_faculty(request_dept, faculty_name):
            return request_dept in faculty_dept_map.get(faculty_name, [])
        
        # Test mappings
        test_cases = [
            ('Computer Science', 'Faculty of Sciences and Information Technology', True),
            ('Civil Engineering', 'Faculty of Engineering and Technology', True),
            ('Computer Science', 'Faculty of Engineering and Technology', False),
            ('Law', 'Faculty of Law', True)
        ]
        
        print("📋 Testing department mappings:")
        all_correct = True
        
        for dept, faculty, expected in test_cases:
            result = belongs_to_faculty(dept, faculty)
            status = "✅" if result == expected else "❌"
            print(f"   {status} {dept} → {faculty}: {result} (expected {expected})")
            if result != expected:
                all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ Error testing department mapping: {e}")
        return False

def check_database_consistency():
    """Check database consistency for requests"""
    try:
        print("\n🔍 Checking database consistency...")
        
        from simple_database_service import get_db_connection
        import pymysql
        
        with get_db_connection() as conn:
            cursor = conn.cursor(pymysql.cursors.DictCursor)
            
            # Check request statuses
            cursor.execute("""
                SELECT status, payment_status, COUNT(*) as count
                FROM transcript_requests 
                GROUP BY status, payment_status
            """)
            status_counts = cursor.fetchall()
            
            print("📊 Request status distribution:")
            for row in status_counts:
                print(f"   - Status: {row['status']}, Payment: {row['payment_status']}, Count: {row['count']}")
            
            # Check department names in users table
            cursor.execute("""
                SELECT DISTINCT department_name 
                FROM users 
                WHERE role = 'student' AND department_name IS NOT NULL
                ORDER BY department_name
            """)
            departments = cursor.fetchall()
            
            print("\n📋 Student departments in database:")
            for dept in departments:
                print(f"   - {dept['department_name']}")
            
            # Check faculty names
            cursor.execute("""
                SELECT DISTINCT faculty_name, department_name
                FROM users 
                WHERE role = 'faculty' AND is_active = 1
                ORDER BY faculty_name
            """)
            faculties = cursor.fetchall()
            
            print("\n👨‍🏫 Faculty in database:")
            for fac in faculties:
                print(f"   - Faculty: {fac['faculty_name']}, Dept: {fac['department_name']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking database: {e}")
        return False

def main():
    """Debug finance and faculty issues"""
    print("🔍 INES Transcript System - Finance & Faculty Issues Debug")
    print("=" * 65)
    
    tests = [
        ("Finance Rejection Workflow", test_finance_rejection_workflow),
        ("Faculty Request Routing", test_faculty_request_routing),
        ("Department Mapping", test_department_mapping),
        ("Database Consistency", check_database_consistency)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"Testing: {test_name}")
        print('='*60)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name} - PASSED")
        else:
            print(f"❌ {test_name} - FAILED")
    
    print(f"\n📊 Debug Results: {passed}/{total} tests passed")
    
    if passed < total:
        print("\n🔧 ISSUES IDENTIFIED:")
        print("1. Check rejection workflow and payment_status updates")
        print("2. Verify faculty department mapping")
        print("3. Ensure notification functions are called")
        print("4. Check database consistency")
    else:
        print("\n🎉 All systems working correctly!")
    
    return passed >= 3

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

"""
Redis Session Interface for Flask
Custom session interface that uses Redis for session storage
"""

import uuid
import json
from datetime import datetime, timedelta
from flask.sessions import SessionInterface, SessionMixin
from werkzeug.datastructures import CallbackDict
from redis_service import get_redis_service

class RedisSession(CallbackDict, SessionMixin):
    """Redis-backed session implementation"""
    
    def __init__(self, initial=None, sid=None, new=False):
        def on_update(self):
            self.modified = True
        
        CallbackDict.__init__(self, initial, on_update)
        self.sid = sid
        self.new = new
        self.modified = False

class RedisSessionInterface(SessionInterface):
    """Session interface that stores sessions in Redis"""
    
    serializer = json
    session_class = RedisSession
    
    def __init__(self, key_prefix='session:', use_signer=False, permanent=True):
        self.key_prefix = key_prefix
        self.use_signer = use_signer
        self.permanent = permanent
        self.redis_service = get_redis_service()
    
    def generate_sid(self):
        """Generate a new session ID"""
        return str(uuid.uuid4())
    
    def get_redis_expiration_time(self, app, session):
        """Get the expiration time for Redis"""
        if session.permanent:
            return app.permanent_session_lifetime
        return timedelta(hours=1)  # Default 1 hour for non-permanent sessions
    
    def open_session(self, app, request):
        """Open a session from Redis"""
        # Get session cookie name - compatible with different Flask versions
        cookie_name = getattr(app, 'session_cookie_name', None) or app.config.get('SESSION_COOKIE_NAME', 'session')
        sid = request.cookies.get(cookie_name)

        if not sid:
            # No session ID in cookie, create new session
            sid = self.generate_sid()
            return self.session_class(sid=sid, new=True)

        # Try to load session from Redis
        session_data = self.redis_service.get_session(sid)

        if session_data is not None:
            # Session found in Redis
            return self.session_class(session_data, sid=sid)

        # Session not found or expired, create new one
        return self.session_class(sid=sid, new=True)
    
    def save_session(self, app, session, response):
        """Save session to Redis"""
        domain = self.get_cookie_domain(app)
        path = self.get_cookie_path(app)
        
        if not session:
            # Empty session, delete from Redis and clear cookie
            if session.modified:
                self.redis_service.delete_session(session.sid)
                cookie_name = getattr(app, 'session_cookie_name', None) or app.config.get('SESSION_COOKIE_NAME', 'session')
                response.delete_cookie(
                    cookie_name,
                    domain=domain,
                    path=path
                )
            return
        
        # Calculate expiration
        redis_exp = self.get_redis_expiration_time(app, session)
        cookie_exp = self.get_expiration_time(app, session)
        
        # Save to Redis
        session_data = dict(session)
        success = self.redis_service.set_session(
            session.sid, 
            session_data, 
            int(redis_exp.total_seconds())
        )
        
        if success:
            # Set cookie
            cookie_name = getattr(app, 'session_cookie_name', None) or app.config.get('SESSION_COOKIE_NAME', 'session')
            response.set_cookie(
                cookie_name,
                session.sid,
                expires=cookie_exp,
                httponly=self.get_cookie_httponly(app),
                domain=domain,
                path=path,
                secure=self.get_cookie_secure(app),
                samesite=self.get_cookie_samesite(app)
            )
        else:
            # Failed to save to Redis, log error
            app.logger.error(f"Failed to save session {session.sid} to Redis")

class EnhancedRedisSessionInterface(RedisSessionInterface):
    """Enhanced Redis session interface with additional features"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.session_timeout = timedelta(hours=24)  # 24 hour default timeout
        self.activity_timeout = timedelta(hours=2)   # 2 hour activity timeout
    
    def open_session(self, app, request):
        """Enhanced session opening with activity tracking"""
        session = super().open_session(app, request)
        
        if not session.new and session.sid:
            # Check if session has been inactive too long
            last_activity = session.get('_last_activity')
            if last_activity:
                try:
                    last_activity_time = datetime.fromisoformat(last_activity)
                    if datetime.now() - last_activity_time > self.activity_timeout:
                        # Session expired due to inactivity
                        self.redis_service.delete_session(session.sid)
                        return self.session_class(sid=self.generate_sid(), new=True)
                except (ValueError, TypeError):
                    # Invalid timestamp, treat as new session
                    pass
        
        return session
    
    def save_session(self, app, session, response):
        """Enhanced session saving with activity tracking"""
        if session and not session.new:
            # Update last activity timestamp
            session['_last_activity'] = datetime.now().isoformat()
            session.modified = True
        
        super().save_session(app, session, response)
    
    def extend_session(self, session_id):
        """Extend session expiration"""
        return self.redis_service.extend_session(
            session_id, 
            int(self.session_timeout.total_seconds())
        )
    
    def get_active_sessions_count(self):
        """Get count of active sessions (if Redis supports it)"""
        try:
            # This would require Redis SCAN command
            # For now, return -1 to indicate not implemented
            return -1
        except Exception:
            return -1
    
    def invalidate_user_sessions(self, user_id):
        """Invalidate all sessions for a specific user"""
        # This would require storing user_id -> session_id mapping
        # For now, we'll implement basic cache invalidation
        self.redis_service.invalidate_student_cache(str(user_id))
        return True

def create_redis_session_interface(app):
    """Factory function to create Redis session interface"""
    
    # Get Redis configuration from app config
    redis_config = {
        'host': app.config.get('REDIS_HOST', 'localhost'),
        'port': app.config.get('REDIS_PORT', 6379),
        'db': app.config.get('REDIS_DB', 0),
        'password': app.config.get('REDIS_PASSWORD', None)
    }
    
    # Initialize Redis service with app config
    from redis_service import init_redis_service
    init_redis_service(**redis_config)
    
    # Create and return session interface
    return EnhancedRedisSessionInterface(
        key_prefix=app.config.get('SESSION_KEY_PREFIX', 'ines:session:'),
        permanent=app.config.get('SESSION_PERMANENT', True)
    )

# Session utilities
def get_session_info(session_id):
    """Get information about a session"""
    redis_service = get_redis_service()
    session_data = redis_service.get_session(session_id)
    
    if session_data:
        return {
            'session_id': session_id,
            'user_id': session_data.get('user_id'),
            'role': session_data.get('role'),
            'name': session_data.get('name'),
            'email': session_data.get('email'),
            'language': session_data.get('language', 'en'),
            'last_activity': session_data.get('_last_activity'),
            'created_at': session_data.get('_created_at')
        }
    
    return None

def create_session_data(user_data):
    """Create session data dictionary from user data"""
    return {
        'user_id': user_data.get('id'),
        'role': user_data.get('role'),
        'name': user_data.get('name'),
        'email': user_data.get('email'),
        'department': user_data.get('department'),
        'language': 'en',  # Default language
        '_created_at': datetime.now().isoformat(),
        '_last_activity': datetime.now().isoformat()
    }

def invalidate_session(session_id):
    """Invalidate a specific session"""
    redis_service = get_redis_service()
    return redis_service.delete_session(session_id)

def extend_session_timeout(session_id, hours=24):
    """Extend session timeout"""
    redis_service = get_redis_service()
    return redis_service.extend_session(session_id, hours * 3600)

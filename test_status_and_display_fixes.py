"""
Test all the status and display fixes:
1. Student status showing correctly (rejected instead of pending)
2. Rejection reason displaying properly in finance history
3. Actual approval/rejection dates in history tables
4. Specific rejection email notification
"""
import sys

def test_student_status_display():
    """Test that student sees correct status (rejected instead of pending)"""
    try:
        print("🔍 Testing student status display...")
        
        from simple_database_service import get_all_requests, add_request, reject_transcript_request, get_requests_by_student_id
        
        # Create and reject a test request
        print("📝 Creating test request...")
        new_request = add_request(
            student_reg_no='STU001',
            academic_years=['2023-2024'],
            payment_method='mobile_money',
            total_price=1000,
            purpose='Academic Records',
            institution_name='INES-Ruhengeri',
            payment_proof_filename='test_payment.jpg'
        )
        
        if new_request:
            request_id = new_request['id']
            print(f"✅ Test request created: {request_id}")
            
            # Reject the request
            print(f"🔄 Rejecting request {request_id}...")
            success = reject_transcript_request(request_id, 'FIN001', 'Insufficient school fees payment')
            
            if success:
                print("✅ Rejection function succeeded")
                
                # Check student view
                student_requests = get_requests_by_student_id('STU001')
                
                # Find our rejected request
                rejected_request = None
                for req in student_requests:
                    if req['id'] == str(request_id):
                        rejected_request = req
                        break
                
                if rejected_request:
                    status = rejected_request['status']
                    reason = rejected_request['rejection_reason']
                    
                    print(f"📊 Student sees:")
                    print(f"   - Status: {status}")
                    print(f"   - Reason: {reason}")
                    
                    if status == 'rejected':
                        print("✅ Student status correctly shows 'rejected'")
                        if reason and reason != '':
                            print("✅ Rejection reason is displayed")
                            return True
                        else:
                            print("❌ Rejection reason is empty")
                            return False
                    else:
                        print(f"❌ Student status shows '{status}' instead of 'rejected'")
                        return False
                else:
                    print("❌ Rejected request not found in student view")
                    return False
            else:
                print("❌ Rejection function failed")
                return False
        else:
            print("❌ Failed to create test request")
            return False
            
    except Exception as e:
        print(f"❌ Error testing student status: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_finance_history_display():
    """Test that finance history shows proper rejection reasons and dates"""
    try:
        print("\n🔍 Testing finance history display...")
        
        from simple_database_service import get_finance_dashboard_data
        
        # Get finance dashboard data
        dashboard_data = get_finance_dashboard_data()
        rejected_requests = dashboard_data['rejected_requests']
        
        print(f"📊 Finance sees {len(rejected_requests)} rejected requests")
        
        if rejected_requests:
            for req in rejected_requests:
                print(f"\n📋 Rejected Request {req['id']}:")
                print(f"   - Reason: {req.get('rejection_reason', 'N/A')}")
                print(f"   - Rejected Date: {req.get('rejected_date', 'N/A')}")
                print(f"   - Student: {req.get('student_name', 'N/A')}")
                
                # Check if reason is properly displayed
                reason = req.get('rejection_reason', '')
                rejected_date = req.get('rejected_date', '')
                
                if reason and reason != 'No reason provided' and reason != '':
                    print("✅ Rejection reason is properly displayed")
                else:
                    print(f"❌ Rejection reason missing or generic: '{reason}'")
                
                if rejected_date and rejected_date != 'N/A':
                    print("✅ Rejection date is displayed")
                else:
                    print(f"❌ Rejection date missing: '{rejected_date}'")
            
            return True
        else:
            print("⚠️  No rejected requests to test display")
            return True
            
    except Exception as e:
        print(f"❌ Error testing finance history: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_approval_dates_display():
    """Test that approval dates are properly displayed"""
    try:
        print("\n🔍 Testing approval dates display...")
        
        from simple_database_service import get_finance_dashboard_data, add_request, approve_transcript_request
        
        # Create and approve a test request
        print("📝 Creating test request for approval...")
        new_request = add_request(
            student_reg_no='STU001',
            academic_years=['2023-2024'],
            payment_method='mobile_money',
            total_price=1000,
            purpose='Academic Records',
            institution_name='INES-Ruhengeri',
            payment_proof_filename='test_payment.jpg'
        )
        
        if new_request:
            request_id = new_request['id']
            print(f"✅ Test request created: {request_id}")
            
            # Approve the request
            print(f"🔄 Approving request {request_id}...")
            success = approve_transcript_request(request_id, 'FIN001')
            
            if success:
                print("✅ Approval function succeeded")
                
                # Check finance dashboard
                dashboard_data = get_finance_dashboard_data()
                approved_requests = dashboard_data['approved_requests']
                
                # Find our approved request
                approved_request = None
                for req in approved_requests:
                    if req['id'] == str(request_id):
                        approved_request = req
                        break
                
                if approved_request:
                    approved_date = approved_request.get('approved_date', 'N/A')
                    print(f"📊 Approved request shows:")
                    print(f"   - Approved Date: {approved_date}")
                    
                    if approved_date and approved_date != 'N/A':
                        print("✅ Approval date is properly displayed")
                        return True
                    else:
                        print("❌ Approval date is missing")
                        return False
                else:
                    print("❌ Approved request not found in finance view")
                    return False
            else:
                print("❌ Approval function failed")
                return False
        else:
            print("❌ Failed to create test request")
            return False
            
    except Exception as e:
        print(f"❌ Error testing approval dates: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_rejection_email_content():
    """Test that rejection email is specific and clear"""
    try:
        print("\n🔍 Testing rejection email content...")
        
        # Test the notification function
        from app import notify_student_rejection
        
        # Sample request data
        sample_data = {
            'id': 999,
            'student_name': 'Test Student',
            'email': '<EMAIL>',
            'academic_years': ['2023-2024']
        }
        
        # Test with specific rejection reason
        test_reason = 'Insufficient school fees payment for academic year 2023-2024'
        
        print("📧 Testing rejection notification...")
        try:
            notify_student_rejection(sample_data, test_reason)
            print("✅ Rejection notification sent successfully")
            print(f"✅ Reason included: {test_reason}")
            return True
        except Exception as e:
            print(f"❌ Rejection notification failed: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing rejection email: {e}")
        return False

def main():
    """Test all status and display fixes"""
    print("🔍 INES Transcript System - Status & Display Fixes Test")
    print("=" * 65)
    
    tests = [
        ("Student Status Display", test_student_status_display),
        ("Finance History Display", test_finance_history_display),
        ("Approval Dates Display", test_approval_dates_display),
        ("Rejection Email Content", test_rejection_email_content)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"Testing: {test_name}")
        print('='*60)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name} - PASSED")
        else:
            print(f"❌ {test_name} - FAILED")
    
    print(f"\n📊 Status & Display Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL STATUS & DISPLAY FIXES WORKING!")
        print("✅ Student sees correct status (rejected instead of pending)")
        print("✅ Finance history shows proper rejection reasons")
        print("✅ Approval/rejection dates are displayed correctly")
        print("✅ Rejection emails are specific and clear")
    else:
        print(f"\n⚠️  {total - passed} issues still need attention")
        
        if passed < total:
            print("\n🔧 REMAINING ISSUES:")
            print("- Check status mapping logic")
            print("- Verify rejection reason extraction")
            print("- Ensure date fields are properly formatted")
            print("- Test email notification content")
    
    return passed >= 3

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

# INES Transcript System - Complete Deployment Guide

## 🚀 System Overview

The INES Transcript System is a comprehensive Flask-based web application that manages academic transcript requests with role-based authentication, payment processing, and automated workflows.

### Key Features:
- **Role-Based Authentication**: Students, Finance Staff, Faculty
- **Secure Payment Processing**: Mobile Money & Flutterwave integration
- **Automated Notifications**: Email notifications at each stage
- **File Management**: Secure upload/download with tracking
- **Real-time Dashboard**: Live statistics and status tracking
- **Multi-language Support**: English/French interface
- **AI Chatbot**: Intelligent user assistance

---

## 🔧 System Requirements

### Server Requirements:
- **OS**: Ubuntu 20.04+ / CentOS 8+ / Windows Server 2019+
- **Python**: 3.8 or higher
- **Database**: MySQL 8.0 or MariaDB 10.5+
- **Memory**: Minimum 4GB RAM (8GB recommended)
- **Storage**: 50GB+ available space
- **Network**: Stable internet connection for email/SMS services

### Dependencies:
```bash
# Core Framework
Flask==2.3.3
Flask-Mail==0.9.1

# Database
PyMySQL==1.1.0
SQLAlchemy==2.0.21

# Security
bcrypt==4.0.1
cryptography==41.0.4

# Payment Processing
requests==2.31.0

# Email Services
sendgrid==6.10.0

# Optional (Performance)
redis==4.6.0
gunicorn==21.2.0
```

---

## 📋 Pre-Installation Setup

### 1. Database Setup

**Create MySQL Database:**
```sql
-- Connect to MySQL as root
mysql -u root -p

-- Create database
CREATE DATABASE ines_transcript_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create application user
CREATE USER 'ines_app'@'localhost' IDENTIFIED BY 'ines_secure_2025!';
GRANT ALL PRIVILEGES ON ines_transcript_system.* TO 'ines_app'@'localhost';
FLUSH PRIVILEGES;

-- Use the database
USE ines_transcript_system;
```

**Create Database Tables:**
```sql
-- Users table
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    reg_no VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role ENUM('student', 'finance', 'faculty') NOT NULL,
    department VARCHAR(255),
    faculty VARCHAR(255),
    enrollment_year INT,
    is_active BOOLEAN DEFAULT TRUE,
    login_count INT DEFAULT 0,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_department (department)
);

-- Departments table
CREATE TABLE departments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) UNIQUE NOT NULL,
    faculty VARCHAR(255) NOT NULL,
    transcript_fee DECIMAL(10,2) DEFAULT 750000.00,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_faculty (faculty)
);

-- Transcript requests table
CREATE TABLE transcript_requests (
    id INT AUTO_INCREMENT PRIMARY KEY,
    request_number VARCHAR(50) UNIQUE NOT NULL,
    student_reg_no VARCHAR(50) NOT NULL,
    student_name VARCHAR(255) NOT NULL,
    student_email VARCHAR(255),
    department VARCHAR(255),
    faculty VARCHAR(255),
    academic_years JSON NOT NULL,
    total_transcripts INT NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    payment_method VARCHAR(50),
    payment_proof_filename VARCHAR(255),
    status ENUM('pending_finance', 'pending_confirmation', 'approved_finance', 'faculty_processing', 'completed', 'rejected', 'done') DEFAULT 'pending_finance',
    auto_decision VARCHAR(20),
    auto_decision_reason TEXT,
    rejection_reason TEXT,
    download_count INT DEFAULT 0,
    last_downloaded TIMESTAMP NULL,
    finance_approved_by VARCHAR(50),
    finance_approved_at TIMESTAMP NULL,
    finance_confirmed_by VARCHAR(50),
    finance_confirmed_at TIMESTAMP NULL,
    rejected_by VARCHAR(50),
    rejected_at TIMESTAMP NULL,
    faculty_processed_by VARCHAR(50),
    faculty_processed_at TIMESTAMP NULL,
    processed_by VARCHAR(50),
    processed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_reg_no) REFERENCES users(reg_no),
    INDEX idx_student (student_reg_no),
    INDEX idx_status (status),
    INDEX idx_department (department)
);

-- Files table
CREATE TABLE files (
    id INT AUTO_INCREMENT PRIMARY KEY,
    request_id INT NOT NULL,
    file_type VARCHAR(50) NOT NULL,
    filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT,
    uploaded_by VARCHAR(50),
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (request_id) REFERENCES transcript_requests(id) ON DELETE CASCADE,
    INDEX idx_request (request_id)
);

-- Student payments table
CREATE TABLE student_payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_reg_no VARCHAR(50) NOT NULL,
    academic_year VARCHAR(20) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_method VARCHAR(50),
    payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    request_id INT,
    department VARCHAR(255),
    FOREIGN KEY (student_reg_no) REFERENCES users(reg_no),
    FOREIGN KEY (request_id) REFERENCES transcript_requests(id),
    INDEX idx_student (student_reg_no),
    INDEX idx_year (academic_year)
);

-- Login logs table (optional, for security monitoring)
CREATE TABLE login_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    email VARCHAR(255),
    role VARCHAR(50),
    success BOOLEAN NOT NULL,
    action VARCHAR(50) DEFAULT 'login',
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user (user_id),
    INDEX idx_email (email),
    INDEX idx_success (success)
);
```

### 2. Environment Configuration

**Create `.env` file:**
```env
# Database Configuration
DB_HOST=localhost
DB_USER=ines_app
DB_PASSWORD=ines_secure_2025!
DB_NAME=ines_transcript_system

# Email Configuration (Gmail SMTP)
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_DEFAULT_SENDER=<EMAIL>

# SendGrid Configuration (Alternative)
SENDGRID_API_KEY=your-sendgrid-api-key

# Security
SECRET_KEY=your-super-secret-key-here
FLASK_ENV=production

# File Upload
UPLOAD_FOLDER=static/uploads
MAX_CONTENT_LENGTH=16777216

# Redis Configuration (Optional)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# Application Settings
TRANSCRIPT_PRICE=1000
```

---

## 🛠️ Installation Steps

### 1. Clone and Setup Project

```bash
# Clone the repository
git clone <repository-url>
cd transcript_system

# Create virtual environment
python3 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### 2. Database Initialization

```bash
# Run the database setup script
python setup_database.py

# Create demo users (optional)
python user_management_system.py
```

### 3. Configure Email Services

**For Gmail SMTP:**
1. Enable 2-Factor Authentication on your Gmail account
2. Generate an App Password
3. Use the App Password in your `.env` file

**For SendGrid:**
1. Create a SendGrid account
2. Generate an API key
3. Add the API key to your `.env` file

### 4. File Permissions

```bash
# Create upload directories
mkdir -p static/uploads/payment_proofs
mkdir -p static/uploads/transcripts

# Set proper permissions
chmod 755 static/uploads
chmod 755 static/uploads/payment_proofs
chmod 755 static/uploads/transcripts
```

---

## 🚀 Running the Application

### Development Mode

```bash
# Activate virtual environment
source venv/bin/activate

# Run the application
python app.py

# Application will be available at http://localhost:5000
```

### Production Mode

**Using Gunicorn:**
```bash
# Install Gunicorn
pip install gunicorn

# Run with Gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 app:app

# Or with configuration file
gunicorn -c gunicorn.conf.py app:app
```

**Gunicorn Configuration (`gunicorn.conf.py`):**
```python
bind = "0.0.0.0:5000"
workers = 4
worker_class = "sync"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100
timeout = 30
keepalive = 2
preload_app = True
```

### Using Nginx (Recommended)

**Nginx Configuration (`/etc/nginx/sites-available/ines-transcript`):**
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /static {
        alias /path/to/your/app/static;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    client_max_body_size 16M;
}
```

**Enable the site:**
```bash
sudo ln -s /etc/nginx/sites-available/ines-transcript /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

---

## 🔐 Security Configuration

### 1. SSL/HTTPS Setup

**Using Let's Encrypt:**
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Obtain SSL certificate
sudo certbot --nginx -d your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 2. Firewall Configuration

```bash
# UFW Firewall
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable
```

### 3. Database Security

```sql
-- Remove test databases
DROP DATABASE IF EXISTS test;

-- Secure MySQL installation
mysql_secure_installation

-- Create backup user
CREATE USER 'backup'@'localhost' IDENTIFIED BY 'secure-backup-password';
GRANT SELECT, LOCK TABLES ON ines_transcript_system.* TO 'backup'@'localhost';
```

---

## 📊 Monitoring and Maintenance

### 1. Log Configuration

**Create log directory:**
```bash
mkdir -p /var/log/ines-transcript
chown www-data:www-data /var/log/ines-transcript
```

**Logging configuration in app:**
```python
import logging
from logging.handlers import RotatingFileHandler

if not app.debug:
    file_handler = RotatingFileHandler('/var/log/ines-transcript/app.log', 
                                       maxBytes=10240000, backupCount=10)
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
    ))
    file_handler.setLevel(logging.INFO)
    app.logger.addHandler(file_handler)
    app.logger.setLevel(logging.INFO)
```

### 2. Database Backup

**Automated backup script (`backup.sh`):**
```bash
#!/bin/bash
BACKUP_DIR="/var/backups/ines-transcript"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="ines_transcript_system"

mkdir -p $BACKUP_DIR

# Database backup
mysqldump -u backup -p'secure-backup-password' $DB_NAME > $BACKUP_DIR/db_backup_$DATE.sql

# File backup
tar -czf $BACKUP_DIR/files_backup_$DATE.tar.gz static/uploads/

# Keep only last 7 days of backups
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
```

**Schedule backup:**
```bash
sudo crontab -e
# Add: 0 2 * * * /path/to/backup.sh
```

### 3. System Monitoring

**Health check endpoint:**
```python
@app.route('/health')
def health_check():
    try:
        # Check database connection
        with get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
        
        return jsonify({
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'database': 'connected'
        })
    except Exception as e:
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500
```

---

## 🔧 Troubleshooting

### Common Issues:

**1. Database Connection Error:**
```bash
# Check MySQL service
sudo systemctl status mysql

# Check connection
mysql -u ines_app -p ines_transcript_system
```

**2. Email Not Sending:**
```bash
# Check SMTP settings in .env
# Test email configuration
python -c "
import smtplib
from email.mime.text import MIMEText
# Test your SMTP settings
"
```

**3. File Upload Issues:**
```bash
# Check permissions
ls -la static/uploads/
# Fix permissions if needed
sudo chown -R www-data:www-data static/uploads/
```

**4. Performance Issues:**
```bash
# Check system resources
htop
df -h
# Monitor database
mysqladmin -u root -p processlist
```

---

## 📈 Performance Optimization

### 1. Database Optimization

```sql
-- Add indexes for better performance
CREATE INDEX idx_requests_created ON transcript_requests(created_at);
CREATE INDEX idx_requests_status_date ON transcript_requests(status, created_at);

-- Optimize tables
OPTIMIZE TABLE users, transcript_requests, files;
```

### 2. Redis Caching (Optional)

```bash
# Install Redis
sudo apt install redis-server

# Configure Redis
sudo systemctl enable redis-server
sudo systemctl start redis-server
```

### 3. Application Optimization

```python
# Enable gzip compression
from flask_compress import Compress
Compress(app)

# Cache static files
@app.after_request
def after_request(response):
    if request.endpoint == 'static':
        response.cache_control.max_age = 31536000  # 1 year
    return response
```

---

## 📞 Support and Maintenance

### Regular Maintenance Tasks:

1. **Weekly:**
   - Check system logs
   - Monitor disk space
   - Review backup status

2. **Monthly:**
   - Update system packages
   - Review security logs
   - Database maintenance

3. **Quarterly:**
   - Security audit
   - Performance review
   - Backup restoration test

### Support Contacts:

- **System Administrator**: IZABAYO JEANLUC SEVERIN
- **Phone**: 0790635888
- **Email**: <EMAIL>

---

## 🎯 Post-Deployment Checklist

- [ ] Database tables created and populated
- [ ] Environment variables configured
- [ ] Email service working
- [ ] File upload/download working
- [ ] All user roles can login
- [ ] Payment processing functional
- [ ] SSL certificate installed
- [ ] Backup system configured
- [ ] Monitoring setup
- [ ] Documentation updated
- [ ] User training completed

---

*This deployment guide ensures a secure, scalable, and maintainable installation of the INES Transcript System. Follow all steps carefully and test thoroughly before going live.*
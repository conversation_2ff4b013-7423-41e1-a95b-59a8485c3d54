<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>INES-Ruhengeri Transcript System - Login</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .dropdown-container {
            display: none;
        }
        .dropdown-container.active {
            display: block;
        }
        .help-link {
            color: #1976D2;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }
        .help-link:hover {
            color: #1565C0;
            text-decoration: underline;
        }
        .login-info {
            text-align: center;
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .role-selection {
            margin: 20px 0;
            text-align: center;
        }
        .role-selection p {
            margin-bottom: 15px;
            color: #666;
            font-weight: 500;
        }
        .role-options {
            display: flex;
            justify-content: center;
            gap: 20px;
        }
        .role-option {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 15px 25px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            background-color: #fff;
        }
        .role-option:hover {
            border-color: #1976D2;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(25, 118, 210, 0.2);
        }
        .role-option.selected {
            border-color: #1976D2;
            background-color: rgba(25, 118, 210, 0.1);
            box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
        }
        .role-option i {
            font-size: 24px;
            margin-bottom: 8px;
            color: #1976D2;
        }
        .role-option span {
            font-weight: 500;
            color: #333;
        }

        /* Chatbot Styles */
        .chat-button {
            position: fixed;
            top: 90px;
            right: 20px;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #083464, #1976D2);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 4px 20px rgba(8, 52, 100, 0.3);
            z-index: 3000;
            transition: all 0.3s ease;
            border: 3px solid white;
        }

        .chat-button:hover {
            transform: scale(1.1) translateY(-2px);
            box-shadow: 0 6px 20px rgba(25, 118, 210, 0.4);
        }

        .chat-window {
            position: fixed;
            top: 160px;
            right: 20px;
            width: 380px;
            height: 550px;
            background-color: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            display: none;
            flex-direction: column;
            z-index: 3000;
            border: 1px solid #e0e0e0;
        }

        .chat-window.active {
            display: flex;
        }

        .chat-header {
            padding: 20px;
            background: linear-gradient(135deg, #083464, #1976D2);
            color: white;
            border-radius: 15px 15px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .chat-header h3 {
            margin: 0;
            font-size: 1.1rem;
        }

        .close-chat {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            font-size: 1.2rem;
        }

        .chat-messages {
            flex: 1;
            padding: 15px;
            overflow-y: auto;
        }

        .message {
            margin-bottom: 10px;
            padding: 8px 12px;
            border-radius: 15px;
            max-width: 80%;
            word-wrap: break-word;
        }

        .message.user {
            background-color: #dcf8c6;
            margin-left: auto;
            border-bottom-right-radius: 5px;
        }

        .message.bot {
            background-color: #e8e8e8;
            margin-right: auto;
            border-bottom-left-radius: 5px;
        }

        .chat-input {
            padding: 15px;
            border-top: 1px solid #eee;
            display: flex;
            gap: 10px;
        }

        .chat-input input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 20px;
            outline: none;
        }

        .chat-input button {
            padding: 12px 16px;
            background: linear-gradient(135deg, #083464, #1976D2);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .chat-input button:hover {
            background: linear-gradient(135deg, #1976D2, #083464);
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(8, 52, 100, 0.3);
        }

        .quick-actions {
            padding: 10px 15px;
            border-top: 1px solid #eee;
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
        }

        .quick-action-btn {
            padding: 5px 10px;
            background-color: #f0f0f0;
            border: 1px solid #ddd;
            border-radius: 15px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .quick-action-btn:hover {
            background-color: #e0e0e0;
        }

        .typing-indicator {
            display: none;
            padding: 8px 12px;
            background-color: #f0f0f0;
            border-radius: 15px;
            margin-bottom: 10px;
            max-width: 80%;
        }

        .typing-indicator.show {
            display: block;
        }

        .typing-dots {
            display: inline-block;
        }

        .typing-dots span {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #999;
            margin: 0 1px;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dots span:nth-child(1) { animation-delay: -0.32s; }
        .typing-dots span:nth-child(2) { animation-delay: -0.16s; }

        @keyframes typing {
            0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }

        /* Navigation Styles */
        .top-nav {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: linear-gradient(135deg, #083464, #1976D2);
            padding: 15px 0;
            z-index: 2000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .nav-logo {
            display: flex;
            align-items: center;
            gap: 10px;
            color: white;
            text-decoration: none;
            font-weight: 700;
            font-size: 18px;
        }

        .nav-logo::before {
            content: "🎓";
            font-size: 24px;
        }

        .nav-links {
            display: flex;
            gap: 20px;
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.9);
            text-decoration: none;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: 25px;
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            color: white;
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .nav-link.active {
            color: white;
            background: rgba(255, 255, 255, 0.2);
        }

        /* Login Page Background */
        body {
            margin: 0;
            padding: 80px 0 0 0;
            min-height: 100vh;
            background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4)), url('{{ url_for("static", filename="images/login_image.jpg") }}');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .login-container {
            min-height: calc(100vh - 80px);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .login-form-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            max-width: calc(450px + 3rem);
            width: 100%;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
        }

        .login-header {
            text-align: center;
            margin-bottom: 30px;
            background: linear-gradient(135deg, #083464, #1976D2);
            padding: 30px 20px;
            border-radius: 20px 20px 0 0;
            margin: -40px -40px 30px -40px;
        }

        .login-header img {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin-bottom: 15px;
        }

        .login-header h1 {
            color: white;
            font-size: 1.8rem;
            margin-bottom: 5px;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .login-header p {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1rem;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }

        .form-header {
            text-align: center;
            margin-bottom: 25px;
        }

        .form-header h2 {
            color: #333;
            font-size: 1.5rem;
            margin-bottom: 8px;
        }

        .form-header p {
            color: #666;
            font-size: 0.9rem;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }

        .input-group {
            position: relative;
            display: block;
        }

        .input-group input,
        .input-group select {
            width: 100%;
            padding: 12px 45px 12px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
            position: relative;
            z-index: 2;
        }

        .input-group input:focus,
        .input-group select:focus {
            outline: none;
            border-color: #083464;
            box-shadow: 0 0 0 3px rgba(8, 52, 100, 0.1);
        }

        /* Ensure input fields are fully clickable */
        .input-group input,
        .input-group select {
            cursor: text;
        }

        .input-group select {
            cursor: pointer;
        }



        .input-icon {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #083464;
            font-size: 18px;
            pointer-events: none;
            z-index: 1;
        }

        .toggle-password {
            position: absolute;
            right: 45px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: #666;
            z-index: 3;
            padding: 5px;
        }

        .btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #083464, #1976D2);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn:hover {
            background: linear-gradient(135deg, #1976D2, #083464);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(8, 52, 100, 0.3);
        }

        .alert {
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #dc3545;
            background: #f8d7da;
            color: #721c24;
        }

        .main-footer {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            text-align: center;
            padding: 15px;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <!-- Top Navigation -->
    <nav class="top-nav">
        <div class="nav-container">
            <a href="{{ url_for('homepage') }}" class="nav-logo">
                INES Transcript System
            </a>
            <div class="nav-links">
                <a href="{{ url_for('homepage') }}" class="nav-link">
                    <i class="fas fa-home"></i> Home
                </a>
                <a href="{{ url_for('login_page') }}" class="nav-link active">
                    <i class="fas fa-sign-in-alt"></i> Login
                </a>
            </div>
        </div>
    </nav>

    <!-- Chat Button -->
    <div class="chat-button" onclick="toggleChat()">
        <i class="fas fa-comments"></i>
    </div>

    <!-- Chat Window -->
    <div class="chat-window" id="chatWindow">
        <div class="chat-header">
            <h3>Transcript System Assistant</h3>
            <button class="close-chat" onclick="toggleChat()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="chat-messages" id="chatMessages">
            <div class="message bot">
                What can I help with? 😊
            </div>
            <div class="typing-indicator" id="typingIndicator">
                <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
                Assistant is typing...
            </div>
        </div>
        <div class="quick-actions">
            <button class="quick-action-btn" onclick="sendQuickMessage('How do I login to the system?')">🔐 Login Help</button>
            <button class="quick-action-btn" onclick="sendQuickMessage('How do I request a transcript?')">📄 Request Guide</button>
            <button class="quick-action-btn" onclick="sendQuickMessage('What are the payment methods?')">💳 Payment Info</button>
            <button class="quick-action-btn" onclick="sendQuickMessage('What are the different user roles?')">👥 User Roles</button>
            <button class="quick-action-btn" onclick="sendQuickMessage('How can I get help or contact support?')">📞 Contact</button>
        </div>
        <div class="chat-input">
            <input type="text" id="userInput" placeholder="Type your message...">
            <button onclick="sendMessage()">
                <i class="fas fa-paper-plane"></i>
            </button>
        </div>
    </div>

    <div class="login-container">
        <div class="login-form-container">
            <div class="login-header">
                <img src="{{ url_for('static', filename='images/ines-logo.png') }}" alt="INES-Ruhengeri Logo" class="logo">
                <h1>Academic Transcript System</h1>
                <p>INES-Ruhengeri</p>
            </div>
            <div class="form-header">
                <h2>Login</h2>
                <p>Please enter your credentials to access the system</p>
            </div>
            
            {% if error %}
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle"></i> {{ error }}
            </div>
            {% endif %}
            
            <div class="role-selection">
                <p>Select your role:</p>
                <div class="role-options">
                    <div class="role-option" onclick="selectRole('student')">
                        <i class="fas fa-user-graduate"></i>
                        <span>Student</span>
                    </div>
                    <div class="role-option" onclick="selectRole('finance')">
                        <i class="fas fa-money-bill-wave"></i>
                        <span>Finance</span>
                    </div>
                    <div class="role-option" onclick="selectRole('faculty')">
                        <i class="fas fa-chalkboard-teacher"></i>
                        <span>Faculty</span>
                    </div>
                </div>
            </div>

            <form action="{{ url_for('login') }}" method="POST" class="login-form">
                <!-- Department Dropdown (for students) -->
                <div id="departmentContainer" class="dropdown-container">
                    <div class="form-group">
                        <label for="department">Department</label>
                        <div class="input-group">
                            <span class="input-icon"><i class="fas fa-building"></i></span>
                            <select id="department" name="department">
                                <option value="">Select Department</option>
                                <option value="Computer Science">Computer Science</option>
                                <option value="Statistics Applied to Economy">Statistics Applied to Economy</option>
                                <option value="Cooperatives Management">Cooperatives Management</option>
                                <option value="Entrepreneurship and SME's Management">Entrepreneurship and SME's Management</option>
                                <option value="French and English">French and English</option>
                                <option value="Civil Engineering">Civil Engineering</option>
                                <option value="Biotechnologies">Biotechnologies</option>
                                <option value="Land Survey">Land Survey</option>
                                <option value="Architecture">Architecture</option>
                                <option value="Water Engineering">Water Engineering</option>
                                <option value="Masonry">Masonry</option>
                                <option value="Welding">Welding</option>
                                <option value="Domestic Plumbing">Domestic Plumbing</option>
                                <option value="Surveying">Surveying</option>
                                <option value="Master of Science in Food Science and Technology">Master of Science in Food Science and Technology</option>
                                <option value="Master of Science in Geo-Informatics">Master of Science in Geo-Informatics</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Faculty Dropdown (for faculty members) -->
                <div id="facultyContainer" class="dropdown-container">
                    <div class="form-group">
                        <label for="faculty">Faculty</label>
                        <div class="input-group">
                            <span class="input-icon"><i class="fas fa-university"></i></span>
                            <select id="faculty" name="faculty">
                                <option value="">Select Faculty</option>
                                <option value="Faculty of Sciences and Information Technology">Faculty of Sciences and Information Technology</option>
                                <option value="Faculty of Economics Social Sciences and Management">Faculty of Economics Social Sciences and Management</option>
                                <option value="Faculty of Education">Faculty of Education</option>
                                <option value="Faculty of Engineering and Technology">Faculty of Engineering and Technology</option>
                                <option value="Faculty of Law and Public Administration">Faculty of Law and Public Administration</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="email">Email</label>
                    <div class="input-group">
                        <span class="input-icon"><i class="fas fa-envelope"></i></span>
                        <input type="email" id="email" name="email" placeholder="Enter your INES email" required>
                    </div>
                </div>

                <div class="form-group">
                    <label for="password">Password</label>
                    <div class="input-group">
                        <span class="input-icon"><i class="fas fa-lock"></i></span>
                        <input type="password" id="password" name="password" placeholder="Enter your password" required>
                        <span class="toggle-password" onclick="togglePassword()">
                            <i class="fas fa-eye"></i>
                        </span>
                    </div>
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-primary btn-block">
                        <i class="fas fa-sign-in-alt"></i> Login
                    </button>
                </div>
            </form>

            <div class="login-info">
                <p>Need help? Contact IT Support: <strong>IZABAYO JEANLUC SEVERIN</strong> at <a href="tel:0790635888" class="help-link">0790635888</a></p>
            </div>
        </div>
    </div>

    <footer class="main-footer">
        <p>&copy; 2025 INES-Ruhengeri. All rights reserved.</p>
    </footer>

    <script>
        let currentRole = '';

        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const icon = document.querySelector('.toggle-password i');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }

        function selectRole(role) {
            currentRole = role;
            const departmentContainer = document.getElementById('departmentContainer');
            const facultyContainer = document.getElementById('facultyContainer');
            const departmentSelect = document.getElementById('department');
            const facultySelect = document.getElementById('faculty');

            // Hide all dropdowns first
            departmentContainer.classList.remove('active');
            facultyContainer.classList.remove('active');

            // Show relevant dropdown based on role
            if (role === 'student') {
                departmentContainer.classList.add('active');
                departmentSelect.required = true;
                facultySelect.required = false;
            } else if (role === 'faculty') {
                facultyContainer.classList.add('active');
                facultySelect.required = true;
                departmentSelect.required = false;
            } else {
                // For finance role, no dropdown is required
                departmentSelect.required = false;
                facultySelect.required = false;
            }

            // Update form action to include role
            const form = document.querySelector('.login-form');
            form.action = "{{ url_for('login') }}?role=" + role;

            // Highlight selected role
            const roleOptions = document.querySelectorAll('.role-option');
            roleOptions.forEach(option => {
                option.classList.remove('selected');
                if (option.querySelector('span').textContent.toLowerCase() === role) {
                    option.classList.add('selected');
                }
            });
        }

        // Chatbot Functions
        function toggleChat() {
            const chatWindow = document.getElementById('chatWindow');
            chatWindow.classList.toggle('active');
        }

        function sendMessage() {
            const input = document.getElementById('userInput');
            const message = input.value.trim();

            if (message) {
                // Add user message to chat
                addMessage(message, 'user');
                input.value = '';

                // Show typing indicator
                showTypingIndicator();

                // Send message to server
                fetch('/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ message: message })
                })
                .then(response => response.json())
                .then(data => {
                    hideTypingIndicator();
                    addMessage(data.response, 'bot');
                })
                .catch(error => {
                    console.error('Error:', error);
                    hideTypingIndicator();
                    addMessage('Sorry, something went wrong. Please try again! 🔧', 'bot');
                });
            }
        }

        function sendQuickMessage(message) {
            const input = document.getElementById('userInput');
            input.value = message;
            sendMessage();
        }

        function showTypingIndicator() {
            const indicator = document.getElementById('typingIndicator');
            indicator.classList.add('show');
            const messagesDiv = document.getElementById('chatMessages');
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        function hideTypingIndicator() {
            const indicator = document.getElementById('typingIndicator');
            indicator.classList.remove('show');
        }

        function addMessage(message, sender) {
            const messagesDiv = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;

            // Convert line breaks to HTML and preserve formatting
            const formattedMessage = message
                .replace(/\n/g, '<br>')
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                .replace(/\*(.*?)\*/g, '<em>$1</em>');

            messageDiv.innerHTML = formattedMessage;
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        // Allow sending message with Enter key
        document.addEventListener('DOMContentLoaded', function() {
            const userInput = document.getElementById('userInput');
            if (userInput) {
                userInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        sendMessage();
                    }
                });
            }
        });
    </script>
</body>
</html>

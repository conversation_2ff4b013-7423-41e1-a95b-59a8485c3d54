#!/usr/bin/env python3
"""
Test different database connection configurations
"""

import pymysql

# Different configurations to try
DB_CONFIGS = [
    {
        'name': 'Current Config (ines_app)',
        'config': {
            'host': 'localhost',
            'user': 'ines_app',
            'password': 'ines_secure_2025!',
            'database': 'ines_transcript_system',
            'charset': 'utf8mb4'
        }
    },
    {
        'name': 'Root User (no password)',
        'config': {
            'host': 'localhost',
            'user': 'root',
            'password': '',
            'database': 'ines_transcript_system',
            'charset': 'utf8mb4'
        }
    },
    {
        'name': 'Root User (password: root)',
        'config': {
            'host': 'localhost',
            'user': 'root',
            'password': 'root',
            'database': 'ines_transcript_system',
            'charset': 'utf8mb4'
        }
    },
    {
        'name': 'Root User (password: password)',
        'config': {
            'host': 'localhost',
            'user': 'root',
            'password': 'password',
            'database': 'ines_transcript_system',
            'charset': 'utf8mb4'
        }
    },
    {
        'name': 'Root User (password: 123456)',
        'config': {
            'host': 'localhost',
            'user': 'root',
            'password': '123456',
            'database': 'ines_transcript_system',
            'charset': 'utf8mb4'
        }
    }
]

def test_connection(config_name, config):
    """Test a database connection configuration"""
    print(f"\n🔍 Testing {config_name}...")
    try:
        connection = pymysql.connect(**config)
        cursor = connection.cursor()
        cursor.execute("SELECT COUNT(*) FROM users")
        count = cursor.fetchone()[0]
        connection.close()
        print(f"✅ SUCCESS! Found {count} users in the database.")
        return True, config
    except Exception as e:
        print(f"❌ FAILED: {e}")
        return False, None

def main():
    """Test all database configurations"""
    print("🚀 Testing database connection configurations...\n")
    
    working_config = None
    
    for db_config in DB_CONFIGS:
        success, config = test_connection(db_config['name'], db_config['config'])
        if success:
            working_config = config
            break
    
    if working_config:
        print(f"\n✅ Found working configuration!")
        print(f"Config: {working_config}")
        
        # Test user data with working config
        print(f"\n🔍 Testing user data with working configuration...")
        try:
            connection = pymysql.connect(**working_config)
            cursor = connection.cursor(pymysql.cursors.DictCursor)
            cursor.execute("SELECT id, email, role, name, reg_no, is_active FROM users LIMIT 5")
            users = cursor.fetchall()
            
            print(f"Found {len(users)} users:")
            for user in users:
                print(f"  - {user['email']} ({user['role']}) - Active: {user['is_active']}")
            
            connection.close()
            
        except Exception as e:
            print(f"❌ Error testing user data: {e}")
    else:
        print(f"\n❌ No working database configuration found!")
        print(f"Please check your MySQL installation and database setup.")

if __name__ == "__main__":
    main()

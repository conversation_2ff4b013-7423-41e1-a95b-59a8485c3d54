#!/usr/bin/env python3
"""
Test web login functionality
"""

import requests
import sys

def test_login(base_url, email, password, role, department=None):
    """Test login via web interface"""
    print(f"\n🔍 Testing login for {email} as {role}...")
    
    # Create session
    session = requests.Session()
    
    # Get login page first to establish session
    try:
        login_page = session.get(f"{base_url}/login")
        print(f"  Login page status: {login_page.status_code}")
    except Exception as e:
        print(f"  ❌ Error accessing login page: {e}")
        return False
    
    # Prepare login data
    login_data = {
        'email': email,
        'password': password
    }
    
    if department:
        if role == 'student':
            login_data['department'] = department
        elif role == 'faculty':
            login_data['faculty'] = department
    
    # Submit login
    try:
        login_url = f"{base_url}/login?role={role}"
        response = session.post(login_url, data=login_data, allow_redirects=False)
        
        print(f"  Login response status: {response.status_code}")
        print(f"  Response headers: {dict(response.headers)}")
        
        if response.status_code == 302:  # Redirect indicates success
            redirect_location = response.headers.get('Location', '')
            print(f"  ✅ Login successful! Redirected to: {redirect_location}")
            
            # Test accessing the dashboard
            dashboard_response = session.get(f"{base_url}{redirect_location}")
            print(f"  Dashboard access status: {dashboard_response.status_code}")
            
            if dashboard_response.status_code == 200:
                print(f"  ✅ Dashboard access successful!")
                return True
            else:
                print(f"  ❌ Dashboard access failed!")
                return False
        else:
            print(f"  ❌ Login failed!")
            print(f"  Response content: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"  ❌ Error during login: {e}")
        return False

def main():
    """Test login for different user types"""
    base_url = "http://localhost:5000"
    
    print("🚀 Testing web login functionality...\n")
    
    # Test cases
    test_cases = [
        {
            'email': '<EMAIL>',
            'password': 'password123',
            'role': 'student',
            'department': 'Computer Science'
        },
        {
            'email': '<EMAIL>',
            'password': 'password123',
            'role': 'finance',
            'department': None
        },
        {
            'email': '<EMAIL>',
            'password': 'password123',
            'role': 'faculty',
            'department': 'Faculty of Sciences and Information Technology'
        }
    ]
    
    success_count = 0
    total_tests = len(test_cases)
    
    for test_case in test_cases:
        success = test_login(
            base_url,
            test_case['email'],
            test_case['password'],
            test_case['role'],
            test_case['department']
        )
        if success:
            success_count += 1
    
    print(f"\n🏁 Test Results: {success_count}/{total_tests} successful")
    
    if success_count == total_tests:
        print("✅ All login tests passed!")
    else:
        print("❌ Some login tests failed!")

if __name__ == "__main__":
    main()

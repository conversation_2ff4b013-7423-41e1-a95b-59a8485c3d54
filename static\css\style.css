/* Main CSS for INES-Ruhengeri Transcript System */

/* Google Fonts Import */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

/* Variables */
:root {
    --primary-color: #1976D2;
    --primary-light: #42A5F5;
    --primary-dark: #1565C0;
    --secondary-color: #00BCD4;
    --secondary-light: #4DD0E1;
    --secondary-dark: #0097A7;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --gray-color: #757575;
    --white-color: #ffffff;
    --shadow-light: 0 2px 5px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 8px rgba(0, 0, 0, 0.1);
    --shadow-dark: 0 8px 16px rgba(0, 0, 0, 0.1);
    --border-radius: 4px;
    --transition-speed: 0.3s;
    --sidebar-width: 250px;
}

/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    font-size: 16px;
    line-height: 1.6;
    color: var(--dark-color);
    background-color: #f0f2f5;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

a {
    text-decoration: none;
    color: var(--primary-color);
    transition: color var(--transition-speed);
}

a:hover {
    color: var(--primary-light);
}

ul {
    list-style: none;
}

/* Login Page Styles */
.login-container {
    max-width: 500px;
    margin: 50px auto;
    background-color: var(--white-color);
    border-radius: 8px;
    box-shadow: var(--shadow-medium);
    overflow: hidden;
}

.login-header {
    background-color: var(--primary-color);
    color: var(--white-color);
    padding: 30px 20px;
    text-align: center;
}

.login-header h1 {
    font-size: 24px;
    margin-bottom: 5px;
}

.login-header p {
    font-size: 16px;
    opacity: 0.8;
}

.logo {
    width: 80px;
    height: auto;
    margin-bottom: 15px;
}

.login-form-container {
    padding: 30px;
}

.form-header {
    text-align: center;
    margin-bottom: 25px;
}

.form-header h2 {
    font-size: 22px;
    color: var(--primary-color);
    margin-bottom: 5px;
}

.form-header p {
    color: var(--gray-color);
    font-size: 14px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--dark-color);
}

.input-group {
    position: relative;
}

.input-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-color);
}

.input-group input {
    width: 100%;
    padding: 12px 12px 12px 40px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    font-size: 16px;
    transition: border-color var(--transition-speed);
}

.input-group input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.toggle-password {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-color);
    cursor: pointer;
}

.btn {
    display: inline-block;
    padding: 12px 24px;
    background-color: var(--primary-color);
    color: var(--white-color);
    border: none;
    border-radius: var(--border-radius);
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color var(--transition-speed);
}

.btn:hover {
    background-color: var(--primary-light);
}

.btn-primary {
    background-color: var(--primary-color);
}

.btn-secondary {
    background-color: var(--secondary-color);
}

.btn-success {
    background-color: var(--success-color);
}

.btn-danger {
    background-color: var(--danger-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.btn-warning {
    background-color: var(--warning-color);
    color: var(--dark-color);
}

.btn-info {
    background-color: var(--info-color);
}

.btn-block {
    display: block;
    width: 100%;
}

.btn i {
    margin-right: 8px;
}

.alert {
    padding: 12px 16px;
    margin-bottom: 20px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
}

.alert i {
    margin-right: 10px;
    font-size: 18px;
}

.alert-success {
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--success-color);
    border: 1px solid var(--success-color);
}

.alert-danger {
    background-color: rgba(244, 67, 54, 0.1);
    color: var(--danger-color);
    border: 1px solid var(--danger-color);
}

.alert-warning {
    background-color: rgba(255, 152, 0, 0.1);
    color: var(--warning-color);
    border: 1px solid var(--warning-color);
}

.alert-info {
    background-color: rgba(33, 150, 243, 0.1);
    color: var(--info-color);
    border: 1px solid var(--info-color);
}

.login-footer {
    margin-top: 30px;
    text-align: center;
}

.login-footer p {
    margin-bottom: 10px;
    color: var(--gray-color);
}

.role-options {
    display: flex;
    justify-content: center;
    gap: 20px;
}

.role-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: background-color var(--transition-speed);
}

.role-option:hover {
    background-color: rgba(26, 35, 126, 0.1);
}

.role-option i {
    font-size: 24px;
    color: var(--primary-color);
    margin-bottom: 5px;
}

.role-option span {
    font-size: 14px;
    color: var(--dark-color);
}

.login-info {
    padding: 15px;
    background-color: rgba(33, 150, 243, 0.1);
    border-top: 1px solid #ddd;
}

.login-info p {
    margin-bottom: 10px;
    font-size: 14px;
    color: var(--dark-color);
}

.login-info ul {
    padding-left: 20px;
    list-style-type: disc;
}

.login-info li {
    font-size: 14px;
    margin-bottom: 5px;
}

.main-footer {
    text-align: center;
    padding: 15px;
    margin-top: auto;
    background-color: var(--primary-dark);
    color: var(--white-color);
}

/* Dashboard Layout */
.dashboard-container {
    display: flex;
    min-height: 100vh;
}

.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    background-color: var(--primary-color);
    color: var(--white-color);
    width: 250px;
    transition: all 0.3s ease;
}

.sidebar-header {
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-header img {
    width: 60px;
    height: auto;
    margin-bottom: 10px;
}

.sidebar-header h2 {
    font-size: 18px;
    margin-bottom: 5px;
}

.sidebar-header p {
    font-size: 14px;
    opacity: 0.8;
}

.sidebar-menu {
    padding: 20px 0;
}

.sidebar-menu ul {
    list-style: none;
}

.sidebar-menu li {
    margin-bottom: 5px;
}

.sidebar-menu a {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: var(--white-color);
    transition: background-color var(--transition-speed);
    position: relative;
}

.sidebar-menu a:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.sidebar-menu a.active {
    background-color: rgba(255, 255, 255, 0.2);
    border-left: 4px solid var(--secondary-color);
}

.sidebar-menu a i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

.badge {
    position: absolute;
    right: 20px;
    background-color: var(--secondary-color);
    color: var(--white-color);
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 10px;
}

.main-content {
    flex: 1;
    margin-left: 250px;
    padding: 20px;
    transition: all 0.3s ease;
}

.top-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #ddd;
}

.menu-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--primary-color);
    font-size: 24px;
    cursor: pointer;
}

.user-info {
    display: flex;
    align-items: center;
}

.user-info img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 10px;
}

.user-details h4 {
    font-size: 16px;
    margin-bottom: 2px;
}

.user-details p {
    font-size: 14px;
    color: var(--gray-color);
}

.user-actions {
    margin-left: 20px;
}

.dashboard-header {
    margin-bottom: 30px;
}

.dashboard-header h1 {
    font-size: 24px;
    color: var(--primary-color);
    margin-bottom: 10px;
}

.dashboard-header p {
    color: var(--gray-color);
}

.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background-color: var(--white-color);
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--shadow-light);
    display: flex;
    align-items: center;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 24px;
}

.pending-icon {
    background-color: rgba(255, 152, 0, 0.1);
    color: var(--warning-color);
}

.approved-icon {
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--success-color);
}

.rejected-icon {
    background-color: rgba(244, 67, 54, 0.1);
    color: var(--danger-color);
}

.requested-icon {
    background-color: rgba(33, 150, 243, 0.1);
    color: var(--info-color);
}

.stat-info h3 {
    font-size: 14px;
    color: var(--gray-color);
    margin-bottom: 5px;
}

.stat-number {
    font-size: 24px;
    font-weight: 600;
    color: var(--dark-color);
}

.card {
    background-color: var(--white-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    margin-bottom: 30px;
    overflow: hidden;
}

.card-header {
    padding: 15px 20px;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h2 {
    font-size: 18px;
    color: var(--primary-color);
}

.card-body {
    padding: 20px;
}

.card-footer {
    padding: 15px 20px;
    border-top: 1px solid #ddd;
    background-color: rgba(0, 0, 0, 0.02);
}

/* Form Styles */
.form-row {
    display: flex;
    flex-wrap: wrap;
    margin: -10px;
}

.form-col {
    flex: 1;
    padding: 10px;
    min-width: 250px;
}

select {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    font-size: 16px;
    transition: border-color var(--transition-speed);
    background-color: var(--white-color);
}

select:focus {
    outline: none;
    border-color: var(--primary-color);
}

textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    font-size: 16px;
    transition: border-color var(--transition-speed);
    resize: vertical;
    min-height: 100px;
}

textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

.checkbox-group {
    margin-bottom: 20px;
}

.checkbox-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.checkbox-item input[type="checkbox"] {
    margin-right: 10px;
}

/* Table Styles */
.table-responsive {
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
}

th, td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

th {
    background-color: rgba(26, 35, 126, 0.05);
    color: var(--primary-color);
    font-weight: 500;
}

tr:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

.status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.status-pending {
    background-color: rgba(255, 152, 0, 0.1);
    color: var(--warning-color);
}

.status-approved {
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--success-color);
}

.status-rejected {
    background-color: rgba(244, 67, 54, 0.1);
    color: var(--danger-color);
}

.status-completed {
    background-color: rgba(33, 150, 243, 0.1);
    color: var(--info-color);
}

.action-buttons {
    display: flex;
    gap: 5px;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.btn-icon {
    width: 36px;
    height: 36px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    padding: 0;
}

/* Payment Methods */
.payment-methods {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.payment-method {
    flex: 1;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-speed);
}

.payment-method:hover {
    border-color: var(--primary-color);
    background-color: rgba(26, 35, 126, 0.05);
}

.payment-method.selected {
    border-color: var(--primary-color);
    background-color: rgba(26, 35, 126, 0.05);
}

.payment-method img {
    height: 40px;
    margin-bottom: 10px;
}

.payment-method h3 {
    font-size: 16px;
    margin-bottom: 5px;
}

.payment-method p {
    font-size: 14px;
    color: var(--gray-color);
}

/* Summary Styles */
.summary-item {
    display: flex;
    justify-content: space-between;
    padding: 10px 0;
    border-bottom: 1px solid #ddd;
}

.summary-item:last-child {
    border-bottom: none;
}

.summary-label {
    font-weight: 500;
}

.summary-value {
    font-weight: 600;
}

.total-row {
    font-size: 18px;
    color: var(--primary-color);
    padding-top: 15px;
    margin-top: 15px;
    border-top: 2px solid #ddd;
}

/* File Upload */
.file-upload {
    border: 2px dashed #ddd;
    border-radius: var(--border-radius);
    padding: 30px;
    text-align: center;
    margin-bottom: 20px;
    transition: border-color var(--transition-speed);
}

.file-upload:hover {
    border-color: var(--primary-color);
}

.file-upload i {
    font-size: 48px;
    color: var(--gray-color);
    margin-bottom: 15px;
}

.file-upload h3 {
    font-size: 18px;
    margin-bottom: 10px;
}

.file-upload p {
    color: var(--gray-color);
    margin-bottom: 15px;
}

.file-input {
    display: none;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.active {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .menu-toggle {
        display: block;
    }
    
    .stats-container {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
    }
    
    .form-col {
        min-width: 100%;
    }
    
    .payment-methods {
        flex-direction: column;
    }
    
    .user-info {
        display: none;
    }
}

@media (max-width: 576px) {
    .stats-container {
        grid-template-columns: 1fr;
    }
    
    .login-container {
        margin: 20px;
    }
    
    .role-options {
        flex-direction: column;
        gap: 10px;
    }
    
    .role-option {
        flex-direction: row;
        justify-content: center;
    }
    
    .role-option i {
        margin-right: 10px;
        margin-bottom: 0;
    }
}

/* Rejection Reason Styles */
.rejection-reason {
    margin-top: 8px;
    padding: 8px;
    background-color: rgba(220, 53, 69, 0.1);
    border-left: 3px solid var(--danger-color);
    border-radius: 4px;
    font-size: 14px;
    color: var(--danger-color);
}

.rejection-reason strong {
    color: var(--danger-color);
    font-weight: 600;
}

/* Status Tabs */
.status-tabs {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 2rem;
    padding: 0 1rem;
}

.tab-button {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    padding: 1.25rem;
    background: var(--white-color);
    border: 2px solid var(--primary-color);
    border-radius: 0.75rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-light);
    font-weight: 500;
    color: var(--primary-color);
}

.tab-button:hover {
    background: var(--primary-color);
    color: var(--white-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.tab-button.active {
    background: var(--primary-color);
    color: var(--white-color);
    box-shadow: var(--shadow-medium);
}

.tab-button i {
    font-size: 1.25rem;
}

.tab-button .badge {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.35rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    font-weight: 600;
}

.tab-content {
    display: none;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Initial Message Card */
#initial-message {
    text-align: center;
    padding: 3rem 2rem;
    background: var(--white-color);
    border-radius: 1rem;
    box-shadow: var(--shadow-light);
    margin: 2rem 0;
    animation: fadeIn 0.5s ease;
}

#initial-message i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
}

#initial-message h3 {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

#initial-message p {
    color: var(--gray-color);
    font-size: 1.1rem;
    max-width: 600px;
    margin: 0 auto;
}

/* Table Styles Enhancement */
.table-responsive {
    background: var(--white-color);
    border-radius: 0.75rem;
    box-shadow: var(--shadow-light);
    overflow: hidden;
}

table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

th {
    background: var(--primary-color);
    color: var(--white-color);
    font-weight: 500;
    padding: 1rem 1.5rem;
    text-align: left;
    font-size: 0.95rem;
}

td {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    font-size: 0.95rem;
}

tr:hover {
    background-color: rgba(0, 51, 102, 0.02);
}

/* Status Badge Enhancement */
.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-size: 0.875rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.status-badge i {
    font-size: 0.875rem;
}

.status-pending {
    background: rgba(255, 193, 7, 0.1);
    color: #856404;
}

.status-approved_finance {
    background: rgba(40, 167, 69, 0.1);
    color: #155724;
}

.status-approved_faculty {
    background: rgba(23, 162, 184, 0.1);
    color: #0c5460;
}

.status-rejected {
    background: rgba(220, 53, 69, 0.1);
    color: #721c24;
}

.status-completed {
    background: rgba(40, 167, 69, 0.1);
    color: #155724;
}

/* Rejection Reason Enhancement */
.rejection-reason {
    background: rgba(220, 53, 69, 0.1);
    color: #721c24;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    max-width: 300px;
    word-wrap: break-word;
    border-left: 3px solid #dc3545;
}

/* Delete Button Enhancement */
.btn-danger {
    background: #dc3545;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
}

.btn-danger:hover {
    background: #c82333;
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(220, 53, 69, 0.2);
}

.btn-danger i {
    font-size: 0.875rem;
}

/* Card Enhancement */
.card {
    background: var(--white-color);
    border-radius: 1rem;
    box-shadow: var(--shadow-light);
    margin-bottom: 2rem;
    overflow: hidden;
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: var(--shadow-medium);
}

.card-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    background: rgba(0, 51, 102, 0.02);
}

.card-header h2 {
    font-size: 1.25rem;
    color: var(--primary-color);
    margin: 0;
    font-weight: 600;
}

.card-body {
    padding: 1.5rem;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .status-tabs {
        flex-direction: column;
        gap: 1rem;
    }

    .tab-button {
        padding: 1rem;
    }

    .table-responsive {
        margin: 0 -1rem;
        border-radius: 0;
    }

    th, td {
        padding: 0.75rem 1rem;
    }
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: var(--white-color);
    margin: 15% auto;
    padding: 0;
    border-radius: 0.5rem;
    width: 90%;
    max-width: 500px;
    box-shadow: var(--shadow-dark);
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-100px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.modal-header {
    padding: 1rem;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    font-size: 1.25rem;
    color: var(--primary-color);
}

.close {
    color: var(--gray-color);
    font-size: 1.5rem;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: var(--dark-color);
}

.modal-body {
    padding: 1rem;
}

.modal-footer {
    padding: 1rem;
    border-top: 1px solid #ddd;
    text-align: right;
}

.modal-footer .btn {
    margin-left: 0.5rem;
}

/* Manage Fees Page Styles */
.table-responsive {
    overflow-x: auto;
    margin: 0 -1rem;
    padding: 0 1rem;
}

.table-responsive table {
    width: 100%;
    border-collapse: collapse;
    margin: 1rem 0;
}

.table-responsive th,
.table-responsive td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #e0e0e0;
}

.table-responsive th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.table-responsive tr:hover {
    background-color: #f8f9fa;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #495057;
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    font-size: 1rem;
    transition: border-color 0.15s ease-in-out;
}

.form-control:focus {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    padding: 1rem;
    border-top: 1px solid #e0e0e0;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.25rem;
    cursor: pointer;
    transition: background-color 0.15s ease-in-out;
}

.btn-secondary:hover {
    background-color: #5a6268;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .table-responsive {
        margin: 0;
        padding: 0;
    }
    
    .table-responsive th,
    .table-responsive td {
        padding: 0.75rem;
    }
    
    .btn-sm {
        padding: 0.375rem 0.75rem;
    }
}

/* Faculty Grid */
.faculty-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    padding: 20px;
}

.faculty-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    cursor: pointer;
    transition: transform 0.2s, box-shadow 0.2s;
    display: flex;
    align-items: center;
    gap: 20px;
}

.faculty-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.faculty-icon {
    width: 60px;
    height: 60px;
    background: #007bff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.faculty-icon i {
    font-size: 24px;
    color: white;
}

.faculty-info h3 {
    margin: 0;
    color: #333;
    font-size: 1.2rem;
}

.faculty-info p {
    margin: 5px 0 0;
    color: #666;
    font-size: 0.9rem;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 1000;
}

.modal-content {
    background: white;
    width: 90%;
    max-width: 800px;
    margin: 50px auto;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    animation: modalSlideIn 0.3s ease-out;
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    color: #333;
    font-size: 1.5rem;
}

.modal-body {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.close {
    font-size: 24px;
    color: #666;
    cursor: pointer;
    transition: color 0.2s;
}

.close:hover {
    color: #333;
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .faculty-grid {
        grid-template-columns: 1fr;
    }
    
    .modal-content {
        width: 95%;
        margin: 20px auto;
    }
}

/* Sidebar styles */
.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    background-color: var(--primary-color);
    color: var(--white-color);
    width: 250px;
    transition: all 0.3s ease;
}

.sidebar .nav-link {
    font-weight: 500;
    color: var(--white-color);
    padding: 0.8rem 1.5rem;
    margin: 0.2rem 0;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--white-color);
}

.sidebar .nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    color: var(--white-color);
}

.sidebar .nav-link i {
    margin-right: 0.8rem;
    width: 20px;
    text-align: center;
}

/* Main content adjustment */
main {
    margin-left: 250px;
    padding: 20px;
    transition: all 0.3s ease;
}

/* User info section in sidebar */
.sidebar-user-info {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 1rem;
}

.sidebar-user-info .user-name {
    color: var(--white-color);
    font-weight: 600;
    margin-bottom: 0.3rem;
}

.sidebar-user-info .user-role {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
}

/* Responsive adjustments */
@media (max-width: 767.98px) {
    .sidebar {
        position: fixed;
        transform: translateX(-100%);
        width: 250px;
    }
    
    .sidebar.active {
        transform: translateX(0);
    }
    
    main {
        margin-left: 0;
    }
}

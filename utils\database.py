"""
Database utilities for INES Transcript System
Connection management and initialization
"""
from flask import current_app

def init_database(app):
    """Initialize database configuration"""
    # Database configuration is handled by simple_database_service
    # This function can be extended for additional database setup
    pass

def get_db_config():
    """Get database configuration from Flask app"""
    return {
        'host': current_app.config['MYSQL_HOST'],
        'user': current_app.config['MYSQL_USER'],
        'password': current_app.config['MYSQL_PASSWORD'],
        'database': current_app.config['MYSQL_DB']
    }
# INES Transcript System - Complete Login Guide

## 🔐 How to Login Using Different Roles

### 📋 Overview
The INES Transcript System supports three main user roles, each with specific permissions and access levels:

1. **👨‍🎓 Students** - Request and track transcripts
2. **💰 Finance Staff** - Verify payments and approve requests  
3. **👨‍🏫 Faculty** - Upload transcripts and process requests

---

## 👨‍🎓 STUDENT LOGIN

### Step-by-Step Process:

1. **Visit the Login Page**
   - Go to the INES Transcript System login page
   - Click on the **Student** role option

2. **Select Your Department**
   - Choose your specific department from the dropdown:
     - Computer Science
     - Statistics Applied to Economy
     - Cooperatives Management
     - French and English
     - Civil Engineering
     - And many more...

3. **Enter Credentials**
   - **Email**: Your INES student email (e.g., <EMAIL>)
   - **Password**: Your student password (default: password123)

4. **Access Student Dashboard**
   - View your request history
   - Submit new transcript requests
   - Track request status
   - Download completed transcripts

### Demo Student Accounts:

**Computer Science:**
- Email: `<EMAIL>`
- Password: `password123`
- Student ID: CS001/2021

**Business Administration:**
- Email: `<EMAIL>`
- Password: `password123`
- Student ID: BA001/2021

**Civil Engineering:**
- Email: `<EMAIL>`
- Password: `password123`
- Student ID: CE001/2021

### Student Capabilities:
✅ Request transcripts for completed academic years  
✅ Make payments via Mobile Money or Flutterwave  
✅ Track request status in real-time  
✅ Download approved transcripts as PDFs  
✅ View payment history and records  
✅ Receive email notifications at each stage  
✅ Delete or modify pending requests  

---

## 💰 FINANCE STAFF LOGIN

### Step-by-Step Process:

1. **Visit the Login Page**
   - Go to the INES Transcript System login page
   - Click on the **Finance** role option

2. **No Department Selection Required**
   - Finance staff don't need to select a department
   - The department dropdown will be hidden

3. **Enter Credentials**
   - **Email**: Your finance staff email
   - **Password**: Your finance password (default: finance123)

4. **Access Finance Dashboard**
   - Review pending payment verifications
   - Approve or reject transcript requests
   - Manage department fee structures
   - View financial reports and history

### Demo Finance Accounts:

**Primary Finance Account:**
- Email: `<EMAIL>`
- Password: `finance123`
- Role: Finance Office

**Finance Assistant:**
- Email: `<EMAIL>`
- Password: `finance123`
- Name: HABIMANA Grace

**Finance Manager:**
- Email: `<EMAIL>`
- Password: `finance123`
- Name: UWAMAHORO Sarah

### Finance Capabilities:
✅ Review and verify student payments  
✅ Approve or reject transcript requests  
✅ View payment proofs and documentation  
✅ Manage department fee structures  
✅ Generate financial reports  
✅ Send notifications to students  
✅ Monitor payment transactions  
✅ Handle payment disputes and issues  

---

## 👨‍🏫 FACULTY LOGIN

### Step-by-Step Process:

1. **Visit the Login Page**
   - Go to the INES Transcript System login page
   - Click on the **Faculty** role option

2. **Select Your Faculty**
   - Choose your faculty from the dropdown:
     - Faculty of Sciences and Information Technology
     - Faculty of Economics Social Sciences and Management
     - Faculty of Education
     - Faculty of Engineering and Technology
     - Faculty of Law and Public Administration

3. **Enter Credentials**
   - **Email**: Your faculty email
   - **Password**: Your faculty password (default: faculty123)

4. **Access Faculty Dashboard**
   - View approved requests awaiting processing
   - Upload transcript files
   - Mark requests as completed
   - View processing history

### Demo Faculty Accounts:

**Faculty Office (General):**
- Email: `<EMAIL>`
- Password: `faculty123`
- Role: Faculty Office

**Sciences Faculty:**
- Email: `<EMAIL>`
- Password: `faculty123`
- Name: Dr. UWIMANA Jean

**Economics Faculty:**
- Email: `<EMAIL>`
- Password: `faculty123`
- Name: Prof. MUKAMANA Alice

**Engineering Faculty:**
- Email: `<EMAIL>`
- Password: `faculty123`
- Name: Dr. NIYONZIMA Eric

### Faculty Capabilities:
✅ View finance-approved requests  
✅ Upload official transcript files  
✅ Process multiple academic years  
✅ Mark requests as completed  
✅ Send completion notifications  
✅ View processing history  
✅ Generate academic reports  
✅ Handle re-upload requests  

---

## 🔧 Troubleshooting Login Issues

### Common Problems & Solutions:

**❌ "Invalid credentials" Error:**
- Check email spelling and case sensitivity
- Verify password is correct
- Ensure you selected the right role
- For students/faculty: verify department/faculty selection

**❌ Department/Faculty Not Showing:**
- Make sure you selected the correct role first
- Refresh the page and try again
- Clear browser cache if needed

**❌ Login Button Not Working:**
- Ensure all required fields are filled
- Check internet connection
- Try a different browser (Chrome recommended)

**❌ Access Denied After Login:**
- Verify you're using the correct role
- Check if your account is active
- Contact IT support if problem persists

### Browser Compatibility:
✅ **Recommended**: Google Chrome, Mozilla Firefox  
✅ **Supported**: Microsoft Edge, Safari  
❌ **Not Recommended**: Internet Explorer  

---

## 🎯 Role-Specific Features

### Student Dashboard Features:
- **Request Transcript**: Submit new requests
- **Request Status**: Track progress
- **View Downloads**: Access completed transcripts
- **Payment History**: View transaction records
- **Profile Settings**: Update contact information

### Finance Dashboard Features:
- **Pending Requests**: Review new submissions
- **Payment Verification**: Check payment proofs
- **Approve/Reject**: Process requests
- **Fee Management**: Update department fees
- **Financial Reports**: Generate reports

### Faculty Dashboard Features:
- **Approved Requests**: View ready-to-process requests
- **Upload Transcripts**: Add transcript files
- **Request History**: View completed uploads
- **Processing Queue**: Manage workflow
- **Academic Reports**: Generate reports

---

## 📞 Support & Contact

### For Login Issues:
**IT Support**: IZABAYO JEANLUC SEVERIN  
**Phone**: **********  
**Email**: <EMAIL>  

### For System Help:
**Administration Office**: INES-Ruhengeri Campus  
**Hours**: Monday-Friday 8:00 AM - 5:00 PM  
**Phone**: +250 788 123 456  

### Emergency Contact:
**24/7 Support**: +250 788 123 456  
**Email**: <EMAIL>  

---

## 🚀 Quick Start Tips

1. **First Time Users**: Use the demo accounts provided above
2. **Students**: Make sure to select your correct department
3. **Faculty**: Choose your faculty before entering credentials
4. **Finance**: No department selection needed
5. **Forgot Password**: Contact IT support for reset
6. **Need Help**: Use the chatbot assistant on the login page

---

## 🔒 Security Best Practices

- **Never share your login credentials**
- **Log out when finished using the system**
- **Use a secure internet connection**
- **Report suspicious activity immediately**
- **Keep your password confidential**
- **Update your password regularly**

---

*This guide covers all aspects of logging into the INES Transcript System. For additional help, contact IT support or visit the Administration Office.*
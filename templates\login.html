<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>INES-Ruhengeri Transcript System - Login</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Navigation Styles */
        .top-nav {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: linear-gradient(135deg, #083464, #1976D2);
            padding: 15px 0;
            z-index: 2000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .nav-logo {
            display: flex;
            align-items: center;
            gap: 10px;
            color: white;
            text-decoration: none;
            font-weight: 700;
            font-size: 18px;
        }

        .nav-logo::before {
            content: "🎓";
            font-size: 24px;
        }

        .nav-links {
            display: flex;
            gap: 20px;
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.9);
            text-decoration: none;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: 25px;
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            color: white;
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .nav-link.active {
            color: white;
            background: rgba(255, 255, 255, 0.2);
        }

        /* Adjust body for login page */
        body {
            padding-top: 80px;
            background: url('{{ url_for("static", filename="images/login_image.jpg") }}') center/cover no-repeat fixed;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* Center login container on background image */
        .login-page-wrapper {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        /* Increase login container width and style */
        .login-container {
            max-width: calc(500px + 3rem) !important;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            margin: 0;
        }

        /* Login header with original colors */
        .login-header {
            background: linear-gradient(135deg, #083464, #1976D2) !important;
            color: white !important;
            padding: 30px 20px;
            text-align: center;
            border-radius: 15px 15px 0 0;
            position: relative;
            z-index: 10;
        }

        .login-header h1 {
            color: white !important;
            font-weight: 600 !important;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            font-size: 24px;
            margin-bottom: 5px;
        }

        .login-header p {
            color: rgba(255, 255, 255, 0.9) !important;
            font-weight: 500 !important;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
            font-size: 16px;
            opacity: 0.9;
        }

        /* Ensure logo is visible */
        .login-header .logo {
            width: 80px !important;
            height: auto !important;
            margin-bottom: 15px !important;
            display: block !important;
        }

        .form-header h2 {
            color: #083464 !important;
            font-weight: 600 !important;
        }

        .form-header p {
            color: #666 !important;
            font-weight: 500 !important;
        }

        /* Style select dropdowns */
        .input-group select {
            width: 100%;
            padding: 12px 12px 12px 40px;
            border: 1px solid #ddd;
            border-radius: var(--border-radius);
            font-size: 16px;
            transition: border-color var(--transition-speed);
            background-color: white;
            appearance: none;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 12px center;
            background-repeat: no-repeat;
            background-size: 16px;
        }

        .input-group select:focus {
            outline: none;
            border-color: #083464;
        }

        .input-group input:focus {
            border-color: #083464 !important;
        }

        .btn-primary {
            background: linear-gradient(135deg, #083464, #1976D2) !important;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #1976D2, #083464) !important;
        }

        /* Ensure role selection text is visible */
        .role-selection p {
            color: #666 !important;
            font-weight: 500 !important;
        }

        /* Footer styling for login page */
        .main-footer {
            background: rgba(8, 52, 100, 0.9);
            backdrop-filter: blur(10px);
            margin-top: 0;
        }
        .dropdown-container {
            display: none;
        }
        .dropdown-container.active {
            display: block;
        }
        .help-link {
            color: #007bff;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }
        .help-link:hover {
            color: #0056b3;
            text-decoration: underline;
        }
        .login-info {
            text-align: center;
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .role-selection {
            margin: 20px 0;
            text-align: center;
        }
        .role-selection p {
            margin-bottom: 15px;
            color: #666;
            font-weight: 500;
        }
        .role-options {
            display: flex;
            justify-content: center;
            gap: 20px;
        }
        .role-option {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 15px 25px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            background-color: #fff;
        }
        .role-option:hover {
            border-color: #007bff;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .role-option.selected {
            border-color: #007bff;
            background-color: #f0f7ff;
        }
        .role-option i {
            font-size: 24px;
            margin-bottom: 8px;
            color: #007bff;
        }
        .role-option span {
            font-weight: 500;
            color: #333;
        }

        /* Chatbot Styles */
        .chat-button {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3);
            z-index: 1000;
            transition: all 0.3s ease;
            border: 3px solid white;
        }

        .chat-button:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 25px rgba(76, 175, 80, 0.4);
        }

        .chat-button i {
            font-size: 24px;
        }

        .chat-window {
            position: fixed;
            top: 90px;
            right: 20px;
            width: 380px;
            height: 550px;
            background-color: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            display: none;
            flex-direction: column;
            z-index: 1000;
            border: 1px solid #e0e0e0;
        }

        .chat-window.active {
            display: flex;
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .chat-header {
            padding: 20px;
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            border-radius: 15px 15px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .chat-header h3 {
            margin: 0;
            font-size: 1.2rem;
            font-weight: 600;
        }

        .close-chat {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            font-size: 1.3rem;
            padding: 5px;
            border-radius: 50%;
            transition: background-color 0.3s;
        }

        .close-chat:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 15px;
            padding: 12px 16px;
            border-radius: 18px;
            max-width: 85%;
            word-wrap: break-word;
            line-height: 1.4;
        }

        .message.user {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            margin-left: auto;
            border-bottom-right-radius: 4px;
        }

        .message.bot {
            background-color: white;
            color: #333;
            margin-right: auto;
            border-bottom-left-radius: 4px;
            border: 1px solid #e0e0e0;
        }

        .chat-input {
            padding: 20px;
            border-top: 1px solid #eee;
            display: flex;
            gap: 12px;
            background: white;
            border-radius: 0 0 15px 15px;
        }

        .chat-input input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            outline: none;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .chat-input input:focus {
            border-color: #4CAF50;
        }

        .chat-input button {
            padding: 12px 16px;
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .chat-input button:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
        }

        .quick-actions {
            padding: 15px 20px;
            border-top: 1px solid #eee;
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            background: white;
        }

        .quick-action-btn {
            padding: 8px 12px;
            background-color: #f0f7ff;
            border: 1px solid #4CAF50;
            border-radius: 20px;
            font-size: 0.85rem;
            cursor: pointer;
            transition: all 0.3s;
            color: #4CAF50;
            font-weight: 500;
        }

        .quick-action-btn:hover {
            background-color: #4CAF50;
            color: white;
            transform: translateY(-1px);
        }

        .typing-indicator {
            display: none;
            padding: 12px 16px;
            background-color: white;
            border: 1px solid #e0e0e0;
            border-radius: 18px;
            margin-bottom: 15px;
            max-width: 85%;
        }

        .typing-indicator.show {
            display: block;
        }

        .typing-dots {
            display: inline-block;
        }

        .typing-dots span {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #4CAF50;
            margin: 0 2px;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dots span:nth-child(1) { animation-delay: -0.32s; }
        .typing-dots span:nth-child(2) { animation-delay: -0.16s; }

        @keyframes typing {
            0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }
    </style>
</head>
<body>
    <!-- Top Navigation -->
    <nav class="top-nav">
        <div class="nav-container">
            <a href="{{ url_for('homepage') }}" class="nav-logo">
                INES Transcript System
            </a>
            <div class="nav-links">
                <a href="{{ url_for('homepage') }}" class="nav-link">
                    <i class="fas fa-home"></i> Home
                </a>
                <a href="{{ url_for('login') }}" class="nav-link active">
                    <i class="fas fa-sign-in-alt"></i> Login
                </a>
            </div>
        </div>
    </nav>

    <!-- Chat Button -->
    <div class="chat-button" onclick="toggleChat()">
        <i class="fas fa-comments"></i>
    </div>

    <!-- Chat Window -->
    <div class="chat-window" id="chatWindow">
        <div class="chat-header">
            <h3>🎓 Transcript Assistant</h3>
            <button class="close-chat" onclick="toggleChat()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="chat-messages" id="chatMessages">
            <div class="message bot">
                What can I help with? 😊
            </div>
            <div class="typing-indicator" id="typingIndicator">
                <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
                Assistant is typing...
            </div>
        </div>
        <div class="quick-actions">
            <button class="quick-action-btn" onclick="sendQuickMessage('How do I login to the system?')">🔐 Login Help</button>
            <button class="quick-action-btn" onclick="sendQuickMessage('How do I request a transcript?')">📄 Request Guide</button>
            <button class="quick-action-btn" onclick="sendQuickMessage('What are the payment methods?')">💳 Payment Info</button>
            <button class="quick-action-btn" onclick="sendQuickMessage('What are the different user roles?')">👥 User Roles</button>
            <button class="quick-action-btn" onclick="sendQuickMessage('How can I get help or contact support?')">📞 Contact</button>
        </div>
        <div class="chat-input">
            <input type="text" id="userInput" placeholder="Type your message...">
            <button onclick="sendMessage()">
                <i class="fas fa-paper-plane"></i>
            </button>
        </div>
    </div>

    <div class="login-page-wrapper">
        <div class="login-container">
        <div class="login-header">
            <img src="{{ url_for('static', filename='images/ines-logo.png') }}" alt="INES-Ruhengeri Logo" class="logo">
            <h1>Academic Transcript System</h1>
            <p>INES-Ruhengeri</p>
        </div>

        <div class="login-form-container">
            <div class="form-header">
                <h2>Login</h2>
                <p>Please enter your credentials to access the system</p>
            </div>

            {% if error %}
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle"></i> {{ error }}
            </div>
            {% endif %}

            <div class="role-selection">
                <p>Select your role:</p>
                <div class="role-options">
                    <div class="role-option" onclick="selectRole('student')">
                        <i class="fas fa-user-graduate"></i>
                        <span>Student</span>
                    </div>
                    <div class="role-option" onclick="selectRole('finance')">
                        <i class="fas fa-money-bill-wave"></i>
                        <span>Finance</span>
                    </div>
                    <div class="role-option" onclick="selectRole('faculty')">
                        <i class="fas fa-chalkboard-teacher"></i>
                        <span>Faculty</span>
                    </div>
                </div>
            </div>

            <form action="{{ url_for('login') }}" method="POST" class="login-form">
                <!-- Department Dropdown (for students) -->
                <div id="departmentContainer" class="dropdown-container">
                    <div class="form-group">
                        <label for="department">Department</label>
                        <div class="input-group">
                            <span class="input-icon"><i class="fas fa-building"></i></span>
                            <select id="department" name="department">
                                <option value="">Select Department</option>
                                <option value="Computer Science">Computer Science</option>
                                <option value="Statistics Applied to Economy">Statistics Applied to Economy</option>
                                <option value="Cooperatives Management">Cooperatives Management</option>
                                <option value="Entrepreneurship and SME's Management">Entrepreneurship and SME's Management</option>
                                <option value="French and English">French and English</option>
                                <option value="Civil Engineering">Civil Engineering</option>
                                <option value="Biotechnologies">Biotechnologies</option>
                                <option value="Land Survey">Land Survey</option>
                                <option value="Architecture">Architecture</option>
                                <option value="Water Engineering">Water Engineering</option>
                                <option value="Masonry">Masonry</option>
                                <option value="Welding">Welding</option>
                                <option value="Domestic Plumbing">Domestic Plumbing</option>
                                <option value="Surveying">Surveying</option>
                                <option value="Master of Science in Food Science and Technology">Master of Science in Food Science and Technology</option>
                                <option value="Master of Science in Geo-Informatics">Master of Science in Geo-Informatics</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Faculty Dropdown (for faculty members) -->
                <div id="facultyContainer" class="dropdown-container">
                    <div class="form-group">
                        <label for="faculty">Faculty</label>
                        <div class="input-group">
                            <span class="input-icon"><i class="fas fa-university"></i></span>
                            <select id="faculty" name="faculty">
                                <option value="">Select Faculty</option>
                                <option value="Faculty of Sciences and Information Technology">Faculty of Sciences and Information Technology</option>
                                <option value="Faculty of Economics Social Sciences and Management">Faculty of Economics Social Sciences and Management</option>
                                <option value="Faculty of Education">Faculty of Education</option>
                                <option value="Faculty of Engineering and Technology">Faculty of Engineering and Technology</option>
                                <option value="Faculty of Law and Public Administration">Faculty of Law and Public Administration</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="email">Email</label>
                    <div class="input-group">
                        <span class="input-icon"><i class="fas fa-envelope"></i></span>
                        <input type="email" id="email" name="email" placeholder="Enter your INES email" required>
                    </div>
                </div>

                <div class="form-group">
                    <label for="password">Password</label>
                    <div class="input-group">
                        <span class="input-icon"><i class="fas fa-lock"></i></span>
                        <input type="password" id="password" name="password" placeholder="Enter your password" required>
                        <span class="toggle-password" onclick="togglePassword()">
                            <i class="fas fa-eye"></i>
                        </span>
                    </div>
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-primary btn-block">
                        <i class="fas fa-sign-in-alt"></i> Login
                    </button>
                </div>
            </form>

            <div class="login-info">
                <p>Need help? Visit <a href="https://www.ines.ac.rw" target="_blank" class="help-link">INES Ruhengeri Official Website</a> for support.</p>
            </div>
        </div>
        </div>
    </div>

    <footer class="main-footer">
        <p>&copy; 2025 INES-Ruhengeri. All rights reserved.</p>
    </footer>

    <script>
        let currentRole = '';

        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const icon = document.querySelector('.toggle-password i');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }

        function selectRole(role) {
            currentRole = role;
            const departmentContainer = document.getElementById('departmentContainer');
            const facultyContainer = document.getElementById('facultyContainer');
            const departmentSelect = document.getElementById('department');
            const facultySelect = document.getElementById('faculty');

            // Hide all dropdowns first
            departmentContainer.classList.remove('active');
            facultyContainer.classList.remove('active');

            // Show relevant dropdown based on role
            if (role === 'student') {
                departmentContainer.classList.add('active');
                departmentSelect.required = true;
                facultySelect.required = false;
            } else if (role === 'faculty') {
                facultyContainer.classList.add('active');
                facultySelect.required = true;
                departmentSelect.required = false;
            } else {
                // For finance role, no dropdown is required
                departmentSelect.required = false;
                facultySelect.required = false;
            }

            // Update form action to include role
            const form = document.querySelector('.login-form');
            form.action = "{{ url_for('login') }}?role=" + role;

            // Highlight selected role
            const roleOptions = document.querySelectorAll('.role-option');
            roleOptions.forEach(option => {
                option.classList.remove('selected');
                if (option.querySelector('span').textContent.toLowerCase() === role) {
                    option.classList.add('selected');
                }
            });
        }

        // Chatbot Functions
        function toggleChat() {
            const chatWindow = document.getElementById('chatWindow');
            chatWindow.classList.toggle('active');
        }

        function sendMessage() {
            const input = document.getElementById('userInput');
            const message = input.value.trim();

            if (message) {
                // Add user message to chat
                addMessage(message, 'user');
                input.value = '';

                // Show typing indicator
                showTypingIndicator();

                // Send message to server
                fetch('/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ message: message })
                })
                .then(response => response.json())
                .then(data => {
                    hideTypingIndicator();
                    addMessage(data.response, 'bot');
                })
                .catch(error => {
                    console.error('Error:', error);
                    hideTypingIndicator();
                    addMessage('Sorry, something went wrong. Please try again! 🔧', 'bot');
                });
            }
        }

        function sendQuickMessage(message) {
            const input = document.getElementById('userInput');
            input.value = message;
            sendMessage();
        }

        function showTypingIndicator() {
            const indicator = document.getElementById('typingIndicator');
            indicator.classList.add('show');
            const messagesDiv = document.getElementById('chatMessages');
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        function hideTypingIndicator() {
            const indicator = document.getElementById('typingIndicator');
            indicator.classList.remove('show');
        }

        function addMessage(message, sender) {
            const messagesDiv = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;

            // Convert line breaks to HTML and preserve formatting
            const formattedMessage = message
                .replace(/\n/g, '<br>')
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                .replace(/\*(.*?)\*/g, '<em>$1</em>');

            messageDiv.innerHTML = formattedMessage;
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        // Allow sending message with Enter key
        document.addEventListener('DOMContentLoaded', function() {
            const userInput = document.getElementById('userInput');
            if (userInput) {
                userInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        sendMessage();
                    }
                });
            }
        });
    </script>
</body>
</html>

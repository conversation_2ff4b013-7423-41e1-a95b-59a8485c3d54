"""
Check the actual table structure to understand the database schema
"""
import sys

def check_table_structure():
    """Check the actual table structure"""
    try:
        print("🔍 Checking actual table structure...")
        
        from simple_database_service import get_db_connection
        import pymysql
        
        with get_db_connection() as conn:
            cursor = conn.cursor()
            
            # Check transcript_requests table structure
            cursor.execute("DESCRIBE transcript_requests")
            columns = cursor.fetchall()
            
            print("📋 transcript_requests table columns:")
            for col in columns:
                print(f"   - {col[0]} ({col[1]}) - {col[3] if col[3] else 'NOT NULL'}")
            
            # Check sample data
            cursor.execute("SELECT * FROM transcript_requests LIMIT 3")
            sample_data = cursor.fetchall()
            
            print(f"\n📊 Sample data ({len(sample_data)} rows):")
            if sample_data:
                # Get column names
                cursor.execute("SHOW COLUMNS FROM transcript_requests")
                column_info = cursor.fetchall()
                column_names = [col[0] for col in column_info]
                
                for i, row in enumerate(sample_data):
                    print(f"\n   Row {i+1}:")
                    for j, value in enumerate(row):
                        if j < len(column_names):
                            print(f"     {column_names[j]}: {value}")
            
            # Check users table structure
            print("\n" + "="*50)
            cursor.execute("DESCRIBE users")
            user_columns = cursor.fetchall()
            
            print("👥 users table columns:")
            for col in user_columns:
                print(f"   - {col[0]} ({col[1]}) - {col[3] if col[3] else 'NOT NULL'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking table structure: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Check table structure"""
    print("🔍 INES Transcript System - Table Structure Check")
    print("=" * 55)
    
    if check_table_structure():
        print("\n✅ Table structure check completed")
    else:
        print("\n❌ Table structure check failed")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

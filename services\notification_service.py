"""
Notification service for INES Transcript System
Email notifications and messaging
"""
import smtplib
from datetime import datetime
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from flask import current_app

def send_email_notification(to_email, subject, template_data):
    """Send HTML email notification"""
    try:
        msg = MIMEMultipart()
        msg['From'] = current_app.config['MAIL_DEFAULT_SENDER']
        msg['To'] = to_email
        msg['Subject'] = subject
        
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background-color: #003366; color: white; padding: 20px; text-align: center; }}
                .content {{ padding: 20px; background-color: #f9f9f9; }}
                .footer {{ text-align: center; padding: 20px; font-size: 12px; color: #666; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>INES-Ruhengeri</h1>
                    <p>Transcript System</p>
                </div>
                <div class="content">
                    {template_data['content']}
                </div>
                <div class="footer">
                    <p>© {datetime.now().year} INES-Ruhengeri. All rights reserved.</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        msg.attach(MIMEText(html_content, 'html'))
        
        with smtplib.SMTP(current_app.config['MAIL_SERVER'], current_app.config['MAIL_PORT']) as server:
            server.starttls()
            server.login(current_app.config['MAIL_USERNAME'], current_app.config['MAIL_PASSWORD'])
            server.send_message(msg)
        
        return True
        
    except Exception as e:
        print(f"Email error: {e}")
        return False

def notify_student_transcript_ready(request_data):
    """Notify student when transcript is ready"""
    try:
        student_email = request_data.get('email')
        if not student_email:
            # Get email from database if not provided
            from simple_database_service import get_db_connection
            with get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT email FROM users WHERE reg_no = %s", 
                             (request_data.get('student_id'),))
                result = cursor.fetchone()
                if result:
                    student_email = result[0]
        
        if not student_email:
            print("No email found for transcript ready notification")
            return
        
        template_data = {
            'content': f"""
                <h2>🎉 Your Transcript is Ready!</h2>
                <p>Dear {request_data.get('student_name', 'Student')},</p>
                <p>Your official transcript has been processed and is ready for download.</p>
                
                <h3>Request Details:</h3>
                <ul>
                    <li><strong>Request ID:</strong> #{request_data.get('id')}</li>
                    <li><strong>Academic Years:</strong> {', '.join(request_data.get('academic_years', []))}</li>
                    <li><strong>Status:</strong> ✅ Completed & Ready</li>
                </ul>
                
                <p><strong>Next Steps:</strong></p>
                <ol>
                    <li>Login to your student dashboard</li>
                    <li>Go to "View Downloads"</li>
                    <li>Download your transcript PDF</li>
                </ol>
                
                <p>Best regards,<br>INES-Ruhengeri Faculty Office</p>
            """
        }
        
        send_email_notification(
            student_email,
            'Your Transcript is Ready for Download - INES-Ruhengeri',
            template_data
        )
        
    except Exception as e:
        print(f"Error sending transcript ready notification: {e}")

def notify_finance_staff(request_data):
    """Notify finance staff of new request"""
    try:
        # Get finance emails
        from simple_database_service import get_db_connection
        with get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT email FROM users WHERE role = 'finance' AND is_active = 1")
            finance_emails = [row[0] for row in cursor.fetchall()]
        
        if not finance_emails:
            finance_emails = ['<EMAIL>']  # Fallback
        
        template_data = {
            'content': f"""
                <h2>🆕 New Transcript Request</h2>
                <p>A new transcript request requires your review.</p>
                
                <h3>Request Details:</h3>
                <ul>
                    <li><strong>Student:</strong> {request_data.get('student_name')}</li>
                    <li><strong>Department:</strong> {request_data.get('department')}</li>
                    <li><strong>Academic Years:</strong> {', '.join(request_data.get('academic_years', []))}</li>
                    <li><strong>Total Amount:</strong> {request_data.get('total_price', 0):,.0f} RWF</li>
                </ul>
                
                <p>Please review and process this request in the finance dashboard.</p>
            """
        }
        
        for email in finance_emails:
            send_email_notification(
                email,
                'New Transcript Request - Finance Review Required',
                template_data
            )
            
    except Exception as e:
        print(f"Error sending finance notification: {e}")
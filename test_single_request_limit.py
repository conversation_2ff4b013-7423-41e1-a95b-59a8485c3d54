#!/usr/bin/env python3
"""
Test script to verify the single request limitation
"""

import requests
import json

def test_single_request_limit():
    """Test that students can only have one pending request at a time"""
    base_url = "http://localhost:5000"
    
    print("🧪 Testing Single Request Limitation")
    print("=" * 50)
    
    # Create a session
    session = requests.Session()
    
    try:
        # Step 1: Login as student
        print("1. Logging in as student...")
        login_data = {
            'username': 'student1',
            'password': 'password123'
        }
        response = session.post(f"{base_url}/login", data=login_data)
        if response.status_code == 200 and 'student' in response.text.lower():
            print("✅ Student login successful")
        else:
            print(f"❌ Login failed: {response.status_code}")
            return
            
        # Step 2: Check dashboard for pending request status
        print("2. Checking dashboard for pending request status...")
        response = session.get(f"{base_url}/student/dashboard")
        if response.status_code == 200:
            if 'You have a pending transcript request' in response.text:
                print("⚠️  Student already has a pending request")
                has_pending = True
            else:
                print("✅ No pending requests found")
                has_pending = False
        else:
            print(f"❌ Dashboard access failed: {response.status_code}")
            return
            
        # Step 3: Try to access transcript request page
        print("3. Trying to access transcript request page...")
        response = session.get(f"{base_url}/student/request-transcript")
        if response.status_code == 200:
            print("✅ Transcript request page accessible")
        else:
            print(f"❌ Transcript request page error: {response.status_code}")
            return
            
        # Step 4: Try to submit a transcript request
        print("4. Trying to submit a transcript request...")
        request_data = {
            'transcript_type': 'official',
            'delivery_method': 'pickup',
            'academic_years': ['2023-2024'],
            'email': '<EMAIL>',
            'purpose': 'Job Application'
        }
        response = session.post(f"{base_url}/student/request-transcript", data=request_data)
        
        print(f"Request submission response status: {response.status_code}")
        print(f"Request submission response URL: {response.url}")
        
        if has_pending:
            # Should be redirected to dashboard with error message
            if 'dashboard' in response.url:
                print("✅ VALIDATION WORKING: Request blocked due to existing pending request")
                print("✅ Student redirected to dashboard as expected")
            else:
                print("❌ VALIDATION FAILED: Request should have been blocked")
        else:
            # Should proceed to summary/payment page
            if response.status_code in [200, 302]:
                if 'summary' in response.url or 'payment' in response.url:
                    print("✅ Request submission successful - no pending requests")
                    
                    # Now try to submit another request (should be blocked)
                    print("\n5. Trying to submit a SECOND request (should be blocked)...")
                    response2 = session.post(f"{base_url}/student/request-transcript", data=request_data)
                    
                    if 'dashboard' in response2.url:
                        print("✅ VALIDATION WORKING: Second request blocked correctly")
                    else:
                        print("❌ VALIDATION FAILED: Second request should have been blocked")
                        
                else:
                    print("⚠️  Request submitted but unexpected redirect")
            else:
                print(f"❌ Request submission failed: {response.status_code}")
                
        print("\n" + "=" * 50)
        print("🎯 SINGLE REQUEST LIMIT TEST COMPLETED")
        
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()

def test_pending_request_check():
    """Test the has_pending_request function directly"""
    print("\n🔍 Testing has_pending_request function directly...")
    
    try:
        from simple_database_service import has_pending_request
        
        # Test with a known student
        student_id = 'STU001'
        has_pending = has_pending_request(student_id)
        
        print(f"Student {student_id} has pending request: {has_pending}")
        
        # Test with another student
        student_id2 = 'student1'
        has_pending2 = has_pending_request(student_id2)
        
        print(f"Student {student_id2} has pending request: {has_pending2}")
        
    except Exception as e:
        print(f"❌ Direct function test failed: {e}")

if __name__ == "__main__":
    test_pending_request_check()
    test_single_request_limit()

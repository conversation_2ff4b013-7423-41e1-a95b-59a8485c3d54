"""
Authentication routes for INES Transcript System
Login, logout, and session management
"""
from flask import Blueprint, render_template, request, redirect, url_for, session, flash
from services.auth_service import authenticate_user
from utils.decorators import redirect_if_authenticated

auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/login', methods=['GET', 'POST'])
@redirect_if_authenticated
def login():
    """User login"""
    if request.method == 'GET':
        return render_template('login_page.html')
    
    email = request.form.get('email', '').strip()
    password = request.form.get('password', '')
    role = request.args.get('role', '')
    department = request.form.get('department') or request.form.get('faculty')
    
    if not all([email, password, role]):
        flash('All fields are required', 'error')
        return render_template('login_page.html')
    
    user = authenticate_user(email, password, department, role)
    
    if user:
        session.clear()
        session.update({
            'user_id': user['reg_no'],
            'email': user['email'],
            'role': user['role'],
            'name': user['name'],
            'department': user.get('department', department),
            'enrollment_year': user.get('enrollment_year', 2021)
        })
        session.permanent = True
        
        # Role-based redirect
        if user['role'] == 'student':
            return redirect(url_for('student.dashboard'))
        elif user['role'] == 'finance':
            return redirect(url_for('finance.dashboard'))
        elif user['role'] == 'faculty':
            return redirect(url_for('faculty.dashboard'))
    
    flash('Invalid credentials. Please try again.', 'error')
    return render_template('login_page.html')

@auth_bp.route('/logout')
def logout():
    """User logout"""
    session.clear()
    flash('You have been logged out successfully', 'info')
    return redirect(url_for('main.homepage'))